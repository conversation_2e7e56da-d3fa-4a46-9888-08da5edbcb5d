import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:automoment/app/modules/bottomnavbar/controllers/bottomnavbar_controller.dart';

void main() {
  group('BottomNavBarController Tests', () {
    late BottomNavBarController controller;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;

      // Create controller without dependencies
      controller = BottomNavBarController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize with default values', () {
      expect(controller.currentIndex, equals(0));
      expect(controller.isInitialized, equals(false));
      expect(controller.isLoading, equals(false));
    });

    test('should have correct number of navigation pages', () {
      expect(controller.navigationPages.length, equals(7));
    });

    test('should check page active status correctly', () {
      expect(controller.isPageActive(0), isTrue);
      expect(controller.isPageActive(1), isFalse);
    });

    test('should validate page indices correctly', () {
      // Test private method through public interface
      // Valid indices should work
      expect(controller.navigationPages.length, equals(7));

      // Test that we have pages 0-6
      for (int i = 0; i < 7; i++) {
        expect(controller.navigationPages[i].index, equals(i));
      }
    });
  });

  group('NavigationPage Tests', () {
    test('should create navigation page correctly', () {
      const page = NavigationPage(
        index: 0,
        title: 'Test',
        iconPath: 'assets/test',
      );

      expect(page.index, equals(0));
      expect(page.title, equals('Test'));
      expect(page.iconPath, equals('assets/test'));
      expect(page.iconOnPath, equals('assets/test_on.png'));
      expect(page.iconOffPath, equals('assets/test_off.png'));
    });
  });
}
