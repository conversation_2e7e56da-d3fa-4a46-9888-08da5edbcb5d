import 'dart:convert';

import 'package:automoment/app/models/notification_model.dart'
    as notification_model;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart'; // Keep Get for Get.put, Get.find if still used by UserController etc.
import 'package:get_storage/get_storage.dart';

import '../controllers/user_controller.dart';
import '../routes/app_router.dart'; // Import the new router
import '../services/api_client.dart';

class NotificationUtil {
  static Future<void> callMarkAsRead(
      String token, int userId, int notificationId) async {
    try {
      await ApiClient.markNotificationAsRead(token, userId, notificationId);
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<void> checkPushNotificationInitialMessage() async {
    // Get any messages which caused the application to open from
    // a terminated state.
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    // If the message also contains a data property with a "type" of "chat",
    // navigate to a chat screen
    if (initialMessage != null) {
      debugPrint("initialMessage: ${initialMessage.data}");

      var type = initialMessage.data['type'];
      var id = initialMessage.data['data'];
      pushNotificationAction(type, id);
    }

    // Also handle any interaction when the app is in the background via a
    // Stream listener
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      var type = message.data['type'];
      var id = message.data['data'];
      pushNotificationAction(type, id);
    });
  }

  void pushNotificationAction(String type, String id) {
    if (type == 'event') {
      // Ensure id is parsed or passed correctly if EVENTS_DETAIL expects a specific type
      AppRouter.router.push(AppRoutePaths.eventsDetail, extra: id);
    } else if (type == 'news') {
      // Ensure id is parsed or passed correctly if newsDetail expects a specific type
      AppRouter.router.push(AppRoutePaths.newsDetail,
          extra: id); // Routes.HOME_DETAIL became AppRoutePaths.newsDetail
    } else if (type == 'info') {
      fetchNotificationDetail(int.parse(id));
    }
  }

  Future<void> fetchNotificationDetail(int id) async {
    try {
      UserController userController = Get.put(UserController());
      var response =
          await ApiClient.getNotificationDetail(userController.getToken(), id);
      notification_model.Notification n =
          notification_model.Notification.fromJson(
              jsonDecode(response)['notification']);
      //var n = Notification.fromJson(jsonDecode(response)['notification']);
      AppRouter.router.push(AppRoutePaths.notificationDetail, extra: n);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> registerTokenAndSubscribe() async {
    // subscribe to global topic
    await FirebaseMessaging.instance.subscribeToTopic('all');

    // FCM Token
    String? fcmToken = await FirebaseMessaging.instance.getToken();
    GetStorage().write('fcmToken', fcmToken);
  }
}
