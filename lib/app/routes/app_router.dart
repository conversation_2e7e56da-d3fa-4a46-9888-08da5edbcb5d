import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:automoment/app/modules/sell_my_slot/controllers/sell_my_slot_controller.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../controllers/user_controller.dart';
import '../services/deep_link_service.dart';

// Views - Add all necessary view imports here
import '../modules/bottombar/views/bottombar_view.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/news/views/news_view.dart';
import '../modules/events/views/events_view.dart';
import '../modules/account/views/account_view.dart';
import '../modules/guest/views/guest_view.dart';
import '../modules/login/views/login_view.dart';
import '../modules/register/views/register_view.dart'; // Add this import
import '../modules/news_detail/views/news_detail_view.dart';
import '../modules/events_detail/views/events_detail_view.dart';
import '../modules/booking/views/booking_view.dart';
import '../modules/check_in/views/check_in_view.dart';
import '../modules/booking_success/views/booking_success_view.dart';
import '../modules/forgot_password_email/views/forgot_password_email_view.dart';
import '../modules/forgot_password_check_code/views/forgot_password_check_code_view.dart';
import '../modules/forgot_password_reset/views/forgot_password_reset_view.dart';
import '../modules/profile_edit/views/profile_edit_view.dart';
import '../modules/events_form/views/events_form_view.dart';
import '../modules/events_form_success/views/events_form_success_view.dart';
import '../modules/events_form_signed/views/events_form_signed_view.dart';
import '../modules/events_form_update/views/events_form_update_view.dart';
import '../modules/events_form_update_success/views/events_form_update_success_view.dart';
import '../modules/vehicles/views/vehicles_view.dart';
import '../modules/vehicles_add/views/vehicles_add_view.dart';
import '../modules/vehicles_edit/views/vehicles_edit_view.dart';
import '../modules/notification/views/notification_view.dart';
import '../modules/notification_detail/views/notification_detail_view.dart';
import '../modules/events_form_sign/views/events_form_sign_view.dart';
import '../modules/events_form_sign_success/views/events_form_sign_success_view.dart';
import '../modules/account_delete/views/account_delete_view.dart';
import '../modules/verify_email/views/verify_email_view.dart';
import '../modules/make_payment/views/make_payment_view.dart';
import '../modules/social_login_form/views/social_login_form_view.dart';
import '../modules/chat_list/views/chat_list_view.dart';
import '../modules/chat/views/chat_view.dart';
import '../modules/leaderboard/views/leaderboard_view.dart';
import '../modules/personal_results/views/personal_results_view.dart';
import '../modules/leaderboard_detail/views/leaderboard_detail_view.dart';
import '../modules/force_upgrade/views/force_upgrade_view.dart';
import '../modules/perlaps/views/perlaps_view.dart';
import '../modules/gallery/views/gallery_view.dart';
import '../modules/gallery_detail/views/gallery_detail_view.dart';
import '../modules/join_waiting_list/views/join_waiting_list_view.dart';
import '../modules/addon/views/addon_view.dart';
// import '../modules/booking_details/views/booking_details_view.dart'; // Assuming this view exists
import '../modules/update_my_booking/views/update_my_booking_view.dart';
import '../modules/sell_my_slot/views/sell_my_slot_view.dart';
import '../modules/reward/views/reward_view.dart';
import '../modules/reward_detail/views/reward_detail_view.dart';
import '../modules/select_coupon_code/views/select_coupon_code_view.dart';
import '../modules/store/views/store_view.dart';
import '../modules/product_detail/views/product_detail_view.dart';
import '../modules/car_listing_detail/views/car_listing_detail_view.dart';
import '../modules/passenger_form/views/passenger_form_view.dart';
import '../modules/group/views/group_view.dart';
import '../modules/claim_rebate/views/claim_rebate_view.dart';
import '../modules/reward_lifestyle_detail/views/reward_lifestyle_detail_view.dart';
import '../modules/cart/views/cart_view.dart';
import '../modules/checkout/views/checkout_view.dart';
import '../modules/shipping_address/views/shipping_address_view.dart';
import '../modules/shipping_address_add/views/shipping_address_add_view.dart';
import '../modules/shipping_address_edit/views/shipping_address_edit_view.dart';
import '../modules/product_payment/views/product_payment_view.dart';
import '../modules/order_status/views/order_status_view.dart';
import '../modules/order_status_detail/views/order_status_detail_view.dart';

// It's good practice to define route names as constants
class AppRoutePaths {
  static const String home = '/home';
  static const String bottomBar = '/';
  static const String news = '/news';
  static const String events = '/events';
  static const String account = '/account';
  static const String guest = '/guest';
  static const String login = '/login';
  static const String register =
      '/register'; // Assuming you have a register view
  static const String newsDetail =
      '/news-detail'; // Renamed from HOME_DETAIL for clarity
  static const String eventsDetail = '/events-detail';
  static const String booking = '/booking';
  static const String checkIn = '/check-in';
  static const String bookingSuccess = '/booking-success';
  static const String forgotPasswordEmail = '/forgot-password-email';
  static const String forgotPasswordCheckCode = '/forgot-password-check-code';
  static const String forgotPasswordReset = '/forgot-password-reset';
  static const String profileEdit = '/profile-edit';
  static const String eventsForm = '/events-form';
  static const String eventsFormSuccess = '/events-form-success';
  static const String eventsFormSigned = '/events-form-signed';
  static const String eventsFormUpdate = '/events-form-update';
  static const String eventsFormUpdateSuccess = '/events-form-update-success';
  static const String vehicles = '/vehicles';
  static const String vehiclesAdd = '/vehicles-add';
  static const String vehiclesEdit = '/vehicles-edit';
  static const String notification = '/notification';
  static const String notificationDetail = '/notification-detail';
  static const String eventsFormSign = '/events-form-sign';
  static const String eventsFormSignSuccess = '/events-form-sign-success';
  static const String accountDelete = '/account-delete';
  static const String verifyEmail = '/verify-email';
  static const String makePayment = '/make-payment';
  static const String socialLoginForm = '/social-login-form';
  static const String chatList = '/chat-list';
  static const String chat = '/chat';
  static const String leaderboard = '/leaderboard';
  static const String personalResults = '/personal-results';
  static const String leaderboardDetail = '/leaderboard-detail';
  static const String forceUpgrade = '/force-upgrade';
  static const String perlaps = '/perlaps';
  static const String gallery = '/gallery';
  static const String galleryDetail = '/gallery-detail';
  static const String joinWaitingList = '/join-waiting-list';
  static const String addon = '/addon';
  static const String bookingDetails = '/booking-details';
  static const String updateMyBooking = '/update-my-booking';
  static const String sellMySlot = '/sell-my-slot';
  static const String reward = '/reward';
  static const String rewardDetail = '/reward-detail';
  static const String selectCouponCode = '/select-coupon-code';
  static const String store = '/store';
  static const String productDetail = '/product-detail';
  static const String carListingDetail = '/car-listing-detail';
  static const String passengerForm = '/passenger-form';
  static const String group = '/group';
  static const String claimRebate = '/claim-rebate';
  static const String rewardLifestyleDetail = '/reward-lifestyle-detail';
  static const String cart = '/cart';
  static const String checkout = '/checkout';
  static const String shippingAddress = '/shipping-address';
  static const String shippingAddressAdd = '/shipping-address-add';
  static const String shippingAddressEdit = '/shipping-address-edit';
  static const String productPayment = '/product-payment';
  static const String orderStatus = '/order-status';
  static const String orderStatusDetail = '/order-status-detail';
}

// Helper function for app version check (adapted from main.dart)
// Ideally, this would be in a shared utility/service file.
Future<bool> isAppLatestVersion() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  int currentBuildVersion = int.tryParse(packageInfo.buildNumber) ?? 0;

  try {
    String responseString = await ApiClient.checkAppVersions();
    Map<String, dynamic> versions = jsonDecode(responseString);

    int latestAndroidVersion =
        int.tryParse(versions['android']?.toString() ?? '0') ?? 0;
    int latestIOSVersion =
        int.tryParse(versions['ios']?.toString() ?? '0') ?? 0;

    debugPrint('Current build version: $currentBuildVersion');
    debugPrint(
        'Latest Android version (latest | current): $latestAndroidVersion | $currentBuildVersion');
    debugPrint(
        'Latest iOS version (latest | current): $latestIOSVersion | $currentBuildVersion');

    if (Platform.isAndroid) {
      return currentBuildVersion >= latestAndroidVersion;
    } else if (Platform.isIOS) {
      return currentBuildVersion >= latestIOSVersion;
    }
    return true; // Default for other platforms
  } catch (e, stackTrace) {
    debugPrint(
        'Error checking app version: $e. Assuming current version is acceptable.');
    Sentry.captureException(e, stackTrace: stackTrace);
    return true; // Proceed if version check fails
  }
}

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation:
        AppRoutePaths.bottomBar, // Default, redirect will override if needed
    observers: [
      FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance),
      SentryNavigatorObserver(),
    ],
    redirect: (BuildContext context, GoRouterState state) async {
      final location = state.uri.toString();
      debugPrint('GoRouter redirect: current location $location');

      // Prevent redirection loops for force_upgrade and guest pages
      if (location == AppRoutePaths.forceUpgrade ||
          location == AppRoutePaths.guest ||
          location == AppRoutePaths.login) {
        return null; // No redirect if already on these specific pages
      }

      // 1. Check for force upgrade
      if (!await isAppLatestVersion()) {
        debugPrint(
            'GoRouter redirect: App needs upgrade, routing to FORCE_UPGRADE');
        return AppRoutePaths.forceUpgrade;
      }

      // Ensure UserController and DeepLinkService are available
      // These should be initialized in main.dart before runApp or here if not already.
      // For simplicity, assuming they are Get.put in main.dart
      final UserController userController = Get.find<UserController>();
      final DeepLinkService deepLinkService = Get.find<DeepLinkService>();

      final bool isLoggedIn = userController.isUserLoggedIn();
      final int? pendingEventId =
          deepLinkService.getPendingEventId(); // Corrected type to int?

      // 2. Handle deep links if user is logged in
      if (pendingEventId != null && isLoggedIn) {
        debugPrint(
            'GoRouter redirect: Found pending event ID: $pendingEventId, preparing for navigation');
        GetStorage().write('initial_event_id', pendingEventId);
        // The actual navigation to event detail will be handled by AppInitializationController
        // or similar logic after the app is ready. Redirect to a common logged-in page.
        if (location != AppRoutePaths.bottomBar) return AppRoutePaths.bottomBar;
        return null; // Already on or heading to bottomBar
      }

      // 3. Redirect based on login status
      if (!isLoggedIn) {
        debugPrint('GoRouter redirect: User not logged in, routing to GUEST');
        // Allow access to login/register pages without redirecting to guest again
        if (location != AppRoutePaths.login &&
            location != AppRoutePaths.register) {
          return AppRoutePaths.guest;
        }
      } else {
        // User is logged in, no pending deep link of concern here
        // If they are trying to access guest or login, redirect to bottomBar
        if (location == AppRoutePaths.guest ||
            location == AppRoutePaths.login) {
          debugPrint(
              'GoRouter redirect: Logged in user accessing guest/login, redirecting to BOTTOMBAR');
          return AppRoutePaths.bottomBar;
        }
      }

      debugPrint(
          'GoRouter redirect: No specific redirection needed for $location');
      return null; // No redirect needed
    },
    routes: <RouteBase>[
      GoRoute(
        path: AppRoutePaths.bottomBar,
        builder: (BuildContext context, GoRouterState state) {
          return const BottomBarView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.home,
        builder: (BuildContext context, GoRouterState state) {
          return HomeView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.news,
        builder: (BuildContext context, GoRouterState state) {
          return NewsView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.events,
        builder: (BuildContext context, GoRouterState state) {
          return EventsView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.account,
        builder: (BuildContext context, GoRouterState state) {
          return AccountView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.guest,
        builder: (BuildContext context, GoRouterState state) {
          return const GuestView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.login,
        builder: (BuildContext context, GoRouterState state) {
          return const LoginView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.register,
        builder: (BuildContext context, GoRouterState state) {
          return const RegisterView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.newsDetail,
        builder: (BuildContext context, GoRouterState state) {
          return NewsDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsDetail,
        builder: (BuildContext context, GoRouterState state) {
          return EventsDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.booking,
        builder: (BuildContext context, GoRouterState state) {
          return BookingView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.checkIn,
        builder: (BuildContext context, GoRouterState state) {
          return CheckInView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.bookingSuccess,
        builder: (BuildContext context, GoRouterState state) {
          return BookingSuccessView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.forgotPasswordEmail,
        builder: (BuildContext context, GoRouterState state) {
          return const ForgotPasswordEmailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.forgotPasswordCheckCode,
        builder: (BuildContext context, GoRouterState state) {
          return const ForgotPasswordCheckCodeView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.forgotPasswordReset,
        builder: (BuildContext context, GoRouterState state) {
          return const ForgotPasswordResetView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.profileEdit,
        builder: (BuildContext context, GoRouterState state) {
          return const ProfileEditView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsForm,
        builder: (BuildContext context, GoRouterState state) {
          return const EventsFormView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsFormSuccess,
        builder: (BuildContext context, GoRouterState state) {
          return EventsFormSuccessView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsFormSigned,
        builder: (BuildContext context, GoRouterState state) {
          return EventsFormSignedView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsFormUpdate,
        builder: (BuildContext context, GoRouterState state) {
          return EventsFormUpdateView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsFormUpdateSuccess,
        builder: (BuildContext context, GoRouterState state) {
          return EventsFormUpdateSuccessView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.vehicles,
        builder: (BuildContext context, GoRouterState state) {
          return VehiclesView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.vehiclesAdd,
        builder: (BuildContext context, GoRouterState state) {
          return const VehiclesAddView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.vehiclesEdit,
        builder: (BuildContext context, GoRouterState state) {
          return const VehiclesEditView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.notification,
        builder: (BuildContext context, GoRouterState state) {
          return const NotificationView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.notificationDetail,
        builder: (BuildContext context, GoRouterState state) {
          return const NotificationDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsFormSign,
        builder: (BuildContext context, GoRouterState state) {
          return EventsFormSignView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.eventsFormSignSuccess,
        builder: (BuildContext context, GoRouterState state) {
          return const EventsFormSignSuccessView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.accountDelete,
        builder: (BuildContext context, GoRouterState state) {
          return const AccountDeleteView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.verifyEmail,
        builder: (BuildContext context, GoRouterState state) {
          return const VerifyEmailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.makePayment,
        builder: (BuildContext context, GoRouterState state) {
          return const MakePaymentView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.socialLoginForm,
        builder: (BuildContext context, GoRouterState state) {
          return const SocialLoginFormView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.chatList,
        builder: (BuildContext context, GoRouterState state) {
          return ChatListView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.chat,
        builder: (BuildContext context, GoRouterState state) {
          return ChatView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.leaderboard,
        builder: (BuildContext context, GoRouterState state) {
          return LeaderboardView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.personalResults,
        builder: (BuildContext context, GoRouterState state) {
          return const PersonalResultsView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.leaderboardDetail,
        builder: (BuildContext context, GoRouterState state) {
          return const LeaderboardDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.forceUpgrade,
        builder: (BuildContext context, GoRouterState state) {
          return const ForceUpgradeView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.perlaps,
        builder: (BuildContext context, GoRouterState state) {
          return PerlapsView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.gallery,
        builder: (BuildContext context, GoRouterState state) {
          return GalleryView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.galleryDetail,
        builder: (BuildContext context, GoRouterState state) {
          return GalleryDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.joinWaitingList,
        builder: (BuildContext context, GoRouterState state) {
          return JoinWaitingListView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.addon,
        builder: (BuildContext context, GoRouterState state) {
          return const AddonView();
        },
      ),
      GoRoute(
        path: AppRoutePaths
            .bookingDetails, // Assuming BookingDetailsView exists and is imported
        builder: (BuildContext context, GoRouterState state) {
          // return BookingDetailsView(); // You'll need to create/import this
          return const Scaffold(
              body: Center(child: Text("Booking Details Placeholder")));
        },
      ),
      GoRoute(
        path: AppRoutePaths.updateMyBooking,
        builder: (BuildContext context, GoRouterState state) {
          return UpdateMyBookingView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.sellMySlot,
        builder: (BuildContext context, GoRouterState state) {
          return SellMySlotView(controller: Get.put(SellMySlotController()));
        },
      ),
      GoRoute(
        path: AppRoutePaths.reward,
        builder: (BuildContext context, GoRouterState state) {
          return RewardView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.rewardDetail,
        builder: (BuildContext context, GoRouterState state) {
          return RewardDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.selectCouponCode,
        builder: (BuildContext context, GoRouterState state) {
          return SelectCouponCodeView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.store,
        builder: (BuildContext context, GoRouterState state) {
          return StoreView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.productDetail,
        builder: (BuildContext context, GoRouterState state) {
          return const ProductDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.carListingDetail,
        builder: (BuildContext context, GoRouterState state) {
          return const CarListingDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.passengerForm,
        builder: (BuildContext context, GoRouterState state) {
          return const PassengerFormView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.group,
        builder: (BuildContext context, GoRouterState state) {
          return GroupView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.claimRebate,
        builder: (BuildContext context, GoRouterState state) {
          return ClaimRebateView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.rewardLifestyleDetail,
        builder: (BuildContext context, GoRouterState state) {
          return const RewardLifestyleDetailView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.cart,
        builder: (BuildContext context, GoRouterState state) {
          return const CartView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.checkout,
        builder: (BuildContext context, GoRouterState state) {
          return const CheckoutView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.shippingAddress,
        builder: (BuildContext context, GoRouterState state) {
          return const ShippingAddressView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.shippingAddressAdd,
        builder: (BuildContext context, GoRouterState state) {
          return const ShippingAddressAddView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.shippingAddressEdit,
        builder: (BuildContext context, GoRouterState state) {
          return const ShippingAddressEditView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.productPayment,
        builder: (BuildContext context, GoRouterState state) {
          return const ProductPaymentView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.orderStatus,
        builder: (BuildContext context, GoRouterState state) {
          return const OrderStatusView();
        },
      ),
      GoRoute(
        path: AppRoutePaths.orderStatusDetail,
        builder: (BuildContext context, GoRouterState state) {
          return const OrderStatusDetailView();
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('Page not found: ${state.error?.message}'),
      ),
    ),
  );
}
