import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/verify_email_controller.dart';

class VerifyEmailView extends StatefulWidget {
  const VerifyEmailView({super.key});

  @override
  State<VerifyEmailView> createState() => _VerifyEmailViewState();
}

class _VerifyEmailViewState extends State<VerifyEmailView> {
  final VerifyEmailController controller = Get.put(VerifyEmailController());

  @override
  void dispose() {
    Get.delete<VerifyEmailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title:
              const Text('Email verification', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        body: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: controller.formKey,
            child: Column(
              children: [
                Text.rich(
                  TextSpan(
                    text:
                        'Let us know that this email address belongs to you. Enter the code from the email sent to ',
                    style: AppTextStyles.normalText,
                    children: <TextSpan>[
                      TextSpan(
                          text: controller.email,
                          style: AppTextStyles.normalTextBold),
                      // can add more TextSpans here...
                    ],
                  ),
                ),
                TextFormField(
                  controller: controller.codeController,
                  keyboardType: TextInputType.number,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Code",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 30,
                ),
                // black button
                ElevatedButton(
                    onPressed: () async {
                      if (controller.formKey.currentState!.validate()) {
                        final bool success = await controller.verifyEmail();

                        // Check if widget is still mounted before proceeding
                        if (!mounted) return;

                        if (success) {
                          GoRouter.of(context).go(
                              AppRoutePaths.bottomBar); // Changed to GoRouter
                        } else {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return KMessageDialogView(
                                  content: controller.msg);
                            },
                          );
                        }
                      }
                    },
                    style: ButtonStyle(
                        foregroundColor:
                            WidgetStateProperty.all<Color>(Colors.white),
                        backgroundColor: WidgetStateProperty.all<Color>(
                            AppColor.primaryButtonColor),
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side: const BorderSide(
                                    color: AppColor.primaryButtonColor)))),
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: const Center(
                        child: Text("Next", style: TextStyle(fontSize: 16)),
                      ),
                    )),
              ],
            ),
          ),
        ));
  }
}
