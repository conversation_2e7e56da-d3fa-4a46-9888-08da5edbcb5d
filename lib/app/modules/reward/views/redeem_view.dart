import 'package:coupon_uikit/coupon_uikit.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/reward.dart';
import '../../shared/dotted_line_painter.dart';
import '../../shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/reward_controller.dart';

class RedeemView extends StatelessWidget {
  RedeemView({super.key});

  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(children: [
          const SizedBox(
            height: 10,
          ),
          ...controller.redeemList.map((reward) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: GestureDetector(
                  onTap: () {
                    controller.selectedRewardToRedeem.value = reward;
                    confirmExchange(context, reward);
                  },
                  child: rewardItemView(reward)),
            );
          }),
          const SizedBox(
            height: 10,
          ),
        ]),
      );
    });
  }

  rewardItemView(Reward reward) {
    const Color primaryColor = Color(0xfff1e3d3);
    const Color secondaryColor = AppColor.secondaryColor;

    String discountStr = "";

    if (reward.couponType == "percentage") {
      discountStr = "${reward.value}%";
    } else {
      discountStr = "SGD ${reward.value}";
    }

    String? couponDesc = reward.name ?? " ";

    return CouponCard(
      height: 150,
      backgroundColor: primaryColor,
      clockwise: true,
      curvePosition: 135,
      curveRadius: 30,
      curveAxis: Axis.vertical,
      borderRadius: 10,
      firstChild: Container(
        decoration: const BoxDecoration(
          color: secondaryColor,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      discountStr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'OFF',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(color: Colors.white54, height: 0),
            const Expanded(
              child: Center(
                child: Text(
                  'AUTOMOMENT',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      secondChild: Container(
        width: double.maxFinite,
        padding: const EdgeInsets.all(18),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Coupon Code',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
                color: Colors.black54,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              "----",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 24,
                color: secondaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Text(
              couponDesc,
              maxLines: 2,
              textAlign: TextAlign.start,
              style: const TextStyle(
                //fontSize: 13,
                color: Colors.black45,
              ),
            ),
            Text(
              "Require ${reward.pointCost} points",
              textAlign: TextAlign.start,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black45,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget redeemCardView(Reward reward) {
    String discountStr = "";

    if (reward.couponType == "percentage") {
      discountStr = "${reward.value}%";
    } else {
      discountStr = "SGD ${reward.value}";
    }

    return Card(
        child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(discountStr,
                    style: AppTextStyles.bigHeaderText
                        .copyWith(color: AppColor.secondaryColor)),
                Text(
                  reward.name!,
                  style: AppTextStyles.normalText,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        //const Spacer(),
        CustomPaint(
          size: const Size(2, 90), // Set the desired height and width
          painter: DashedLineVerticalPainter(),
        ),

        Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
              child: reward.image != null
                  ? Image.network(
                      reward.image!,
                      fit: BoxFit.fitWidth,
                      width: 90,
                      // height: 90,
                    )
                  : Container(
                      width: 90,
                    ),
            ),
            Text(
              "${reward.pointCost} points",
              style: AppTextStyles.normalText.copyWith(fontSize: 11),
            )
          ],
        ),
      ],
    ));
  }

  confirmExchange(BuildContext context, Reward reward) {
    showDialog(
      context: context,
      builder: (BuildContext context) => KConfirmDialogView(
        content: "Are you sure want to exchange your points?",
        callbackNo: () {
          Navigator.of(context).pop();
        },
        callbackYes: () async {
          if (!await controller.pointExchange()) {
            showDialog(
              context: context,
              builder: (BuildContext context) => KMessageDialogView(
                content: controller.msg,
                callback: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
              ),
            );
          } else {
            // reload
            controller.fetchRewards();
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(controller.msg)),
            );
          }
        },
      ),
    );
  }
}
