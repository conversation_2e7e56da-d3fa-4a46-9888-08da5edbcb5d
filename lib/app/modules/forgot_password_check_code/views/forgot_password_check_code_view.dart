import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/forgot_password_check_code_controller.dart';

class ForgotPasswordCheckCodeView extends StatefulWidget {
  const ForgotPasswordCheckCodeView({super.key});

  @override
  State<ForgotPasswordCheckCodeView> createState() =>
      _ForgotPasswordCheckCodeViewState();
}

class _ForgotPasswordCheckCodeViewState
    extends State<ForgotPasswordCheckCodeView> {
  final ForgotPasswordCheckCodeController controller =
      Get.put(ForgotPasswordCheckCodeController());

  @override
  void dispose() {
    Get.delete<ForgotPasswordCheckCodeController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Reset Password Request',
            style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Form(
          key: controller.formKey,
          child: ListView(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Text(
                  'Please enter the code',
                  style: AppTextStyles.normalText,
                ),
              ),
              TextFormField(
                controller: controller.codeController,
                keyboardType: TextInputType.number,
                autocorrect: false,
                decoration: const InputDecoration(
                  labelText: "Password request code",
                  hintText: "",
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required';
                  }
                  return null;
                },
              ),
              const SizedBox(
                height: 30,
              ),
              // black button
              ElevatedButton(
                  onPressed: () async {
                    if (controller.formKey.currentState!.validate()) {
                      if (await controller
                          .checkCode(controller.codeController.value.text)) {
                        context.push(AppRoutePaths.forgotPasswordReset,
                            extra: controller.codeController.value.text);
                      } else {
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) =>
                              KMessageDialogView(content: controller.msg),
                        );
                      }
                    }
                  },
                  style: ButtonStyle(
                      foregroundColor:
                          WidgetStateProperty.all<Color>(Colors.white),
                      backgroundColor: WidgetStateProperty.all<Color>(
                          AppColor.primaryButtonColor),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                              side: const BorderSide(
                                  color: AppColor.primaryButtonColor)))),
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 50,
                    child: const Center(
                      child: Text("Next", style: TextStyle(fontSize: 16)),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
