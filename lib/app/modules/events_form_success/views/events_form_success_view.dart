import 'package:automoment/app/modules/events_detail/controllers/events_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../shared/support_button.dart';
import '../controllers/events_form_success_controller.dart';

class EventsFormSuccessView extends StatelessWidget {
  EventsFormSuccessView({super.key});

  final EventsFormSuccessController controller =
      Get.put(EventsFormSuccessController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Indemnity Form', style: AppTextStyles.titleText),
        elevation: 1,
        automaticallyImplyLeading: false,
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: ListView(
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 20),
              child: Text(
                'You have successfully submitted Indemnity Form for this event.',
                style: AppTextStyles.normalText,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                controller.event.value.title!,
                style: AppTextStyles.normalTextBold,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'Event Date: ${controller.showEventDate()}',
                style: AppTextStyles.normalText,
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            // black button
            ElevatedButton(
                onPressed: () {
                  final EventsDetailController eventController = Get.find();
                  eventController.fetchCheckBookingStatus();
                  // Navigate to events detail, effectively clearing until that route
                  GoRouter.of(context).go(AppRoutePaths.eventsDetail);
                },
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(
                        AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.primaryButtonColor)))),
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  child: const Center(
                    child: Text("Back", style: TextStyle(fontSize: 16)),
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
