import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import
import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../helpers/debug_helper.dart';
import '../../../services/api_client.dart';
import '../../shipping_address/controllers/shipping_address_controller.dart';

class ShippingAddressAddController extends BaseScreenController {
  @override
  String get screenName => 'Add Shipping Address';
  final formKey = GlobalKey<FormState>();
  final userController = Get.find<UserController>();

  final fullNameController = TextEditingController();
  final phoneController = TextEditingController();
  final addressLine1Controller = TextEditingController();
  final addressLine2Controller = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final postalCodeController = TextEditingController();
  final selectedCountry = 'Singapore'.obs;

  final isLoading = false.obs;

  @override
  void onClose() {
    fullNameController.dispose();
    phoneController.dispose();
    addressLine1Controller.dispose();
    addressLine2Controller.dispose();
    cityController.dispose();
    stateController.dispose();
    postalCodeController.dispose();
    super.onClose();
  }

  Future<void> saveAddress() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isLoading.value = true;

      // Check if this will be the first address
      final addressesResponse =
          await ApiClient.getShippingAddresses(userController.getToken());
      final isFirstAddress = addressesResponse['success'] == true &&
          (addressesResponse['data'] == null ||
              (addressesResponse['data'] as List).isEmpty);

      final address = {
        'full_name': fullNameController.text,
        'phone': phoneController.text,
        'address_line1': addressLine1Controller.text,
        'address_line2': addressLine2Controller.text.isNotEmpty
            ? addressLine2Controller.text
            : null,
        'city': cityController.text.isNotEmpty ? cityController.text : null,
        'state': stateController.text.isNotEmpty ? stateController.text : null,
        'postal_code': postalCodeController.text.isNotEmpty
            ? postalCodeController.text
            : null,
        'country': selectedCountry.value,
        'is_default':
            isFirstAddress, // Set as default if it's the first address
      };

      final response = await ApiClient.addShippingAddress(
        userController.getToken(),
        address,
      );

      if (response['success'] == true && response['data'] != null) {
        final addressData = response['data'];
        if (Get.context != null) {
          GoRouter.of(Get.context!)
              .pop(addressData); // Changed to GoRouter.of(Get.context!).pop
        }
        Get.find<ShippingAddressController>().fetchAddresses();
      }
    } catch (e) {
      DebugHelper.d('Error adding address: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
