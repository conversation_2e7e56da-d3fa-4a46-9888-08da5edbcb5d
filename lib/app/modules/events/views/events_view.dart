import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:go_router/go_router.dart'; // Add GoRouter import
import 'package:group_button/group_button.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../../routes/app_router.dart'; // Add AppRouter import
import '../controllers/events_controller.dart';

class EventsView extends StatelessWidget {
  EventsView({super.key});

  final EventsController controller = Get.put(EventsController());
  final GlobalKey<ScaffoldState> filterDrawerKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: filterDrawer<PERSON><PERSON>,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        title: const Text(
          "Upcoming Events",
          style: TextStyle(
              color: AppColor.primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 16),
        ),
        elevation: 1,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
          icon: const Icon(Icons.menu),
          iconSize: 30,
          color: Colors.black,
        ),
        actions: [
          IconButton(
            padding: const EdgeInsets.only(right: 0),
            onPressed: () {
              filterDrawerKey.currentState!.openEndDrawer();
            },
            icon: const ImageIcon(AssetImage('assets/images/filter.png'),
                color: Colors.black, size: 30),
          ),
          IconButton(
            padding: const EdgeInsets.only(right: 10),
            onPressed: () {
              GoRouter.of(context).push(AppRoutePaths.notification);
            },
            icon: Obx(() {
              return Stack(
                children: [
                  const Icon(Icons.notifications,
                      color: Colors.black, size: 30),
                  (controller.userController.unreadNotificationCount.value > 0)
                      ? Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6)),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            ),
                            child: Text(
                              (controller.userController.unreadNotificationCount
                                          .value >
                                      99)
                                  ? "99+"
                                  : controller.userController
                                      .unreadNotificationCount.value
                                      .toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                      : const SizedBox()
                ],
              );
            }),
          ),
        ],
      ),
      endDrawer: Directionality(
        textDirection: TextDirection.ltr,
        child: Drawer(
          child: filterFormWidget(
              context, controller.filterOrganisers, controller.filterMonths),
        ),
      ),
      body: Obx(() => buildBody()),
    );
  }

  Widget buildBody() {
    return Column(
      children: [
        Expanded(child: getBodySection()),
      ],
    );
  }

  getBodySection() {
    return getListEvents();
  }

  getListEvents() {
    return RefreshIndicator(
      onRefresh: () {
        return Future.delayed(const Duration(seconds: 1), () {
          controller.fetchEvents();
        });
      },
      child: ListView.builder(
          scrollDirection: Axis.vertical,
          itemCount: controller.eventList.length,
          itemBuilder: (BuildContext context, int itemIndex) {
            return GestureDetector(
              onTap: () {
                GoRouter.of(context).push(AppRoutePaths.eventsDetail,
                    extra: controller.eventList[itemIndex]);
              },
              child: buildItemList(itemIndex),
            );
          }),
    );
  }

  buildItemList(int itemIndex) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              child: Container(
                constraints: const BoxConstraints(
                  maxHeight: 200,
                ),
                //height: 200,
                child: FadeInImage(
                  placeholder:
                      const AssetImage('assets/images/image_placeholder.png'),
                  image: NetworkImage(
                    "${AppConfig.storageUrl}${controller.eventList[itemIndex].image}",
                  ),
                  width: Get.width,
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Text(controller.eventList[itemIndex].title ?? "",
                  maxLines: 3,
                  style: AppTextStyles.smallTitleBold,
                  overflow: TextOverflow.ellipsis),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  left: AppConfig.defaultPadding,
                  right: AppConfig.defaultPadding,
                  bottom: AppConfig.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    //"date here",
                    StringUtil.dateTimeToString(
                        controller.eventList[itemIndex].eventDate!),
                    style:
                        AppTextStyles.smallText.copyWith(color: Colors.black),
                  ),
                  controller.eventList[itemIndex].organizer != null
                      ? Text(
                          "|   Organised by ${controller.eventList[itemIndex].organizer!}",
                          style: AppTextStyles.smallText
                              .copyWith(color: Colors.black),
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildItemList2homestyle(int itemIndex) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          alignment: Alignment.center,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 100),
                child: SizedBox(
                  height: 200,
                  child: Image.network(
                    "${AppConfig.storageUrl}${controller.eventList[itemIndex].image}",
                    fit: BoxFit.fitWidth,
                    width: Get.width,
                  ),
                ),
              ),
              controller.eventList[itemIndex].title == ""
                  ? const SizedBox()
                  : Container(
                      height: 100,
                      width: Get.width - 40,
                      decoration: BoxDecoration(
                          boxShadow: const [
                            BoxShadow(
                                offset: Offset(0, 1),
                                blurRadius: 5,
                                color: Colors.black26)
                          ],
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20)),
                      margin: const EdgeInsets.only(bottom: 50),
                      child: Row(
                        children: [
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: AppConfig.defaultPadding * 2,
                                      left: AppConfig.defaultPadding * 2,
                                      right: AppConfig.defaultPadding * 2,
                                      bottom: AppConfig.defaultPadding),
                                  child: Text(
                                      controller.eventList[itemIndex].title ??
                                          "",
                                      maxLines: 1,
                                      style: AppTextStyles.smallTitleBold,
                                      overflow: TextOverflow.ellipsis),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: AppConfig.defaultPadding * 2,
                                      right: AppConfig.defaultPadding * 2,
                                      bottom: AppConfig.defaultPadding * 2),
                                  child: ExtendedText(
                                    controller.eventList[itemIndex].eventDate
                                        .toString(),
                                    maxLines: 2,
                                    style: AppTextStyles.smallText
                                        .copyWith(color: Colors.black),
                                    overflowWidget: const TextOverflowWidget(
                                      maxHeight: 40,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: <Widget>[
                                          Text('\u2026 '),
                                          Text(
                                            "See more",
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: AppColor.secondaryColor),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(right: 5),
                            child: IconButton(
                              icon: const Icon(Icons.chevron_right,
                                  color: AppColor.secondaryColor, size: 30),
                              onPressed: () {},
                            ),
                          )
                        ],
                      ),
                    ),
            ],
          ),
        ),
      ],
    );
  }

  filterFormWidget(BuildContext context, organisers, months) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
        height: 90,
        width: Get.width,
        decoration: BoxDecoration(
          color: Colors.grey[200],
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 10, top: 60, right: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text("Filter", style: AppTextStyles.titleText),
              GestureDetector(
                onTap: () {
                  controller.organiserController.unselectAll();
                  controller.monthController.unselectAll();
                  controller.filterDataAndReload();
                },
                child: const Text("Clear", style: AppTextStyles.titleText),
              ),
            ],
          ),
        ),
      ),
      const Padding(
        padding: EdgeInsets.only(left: 10.0, top: 20),
        child: Text("Organiser"),
      ),
      Padding(
        padding: const EdgeInsets.all(10.0),
        child: GroupButton(
          controller: controller.organiserController,
          isRadio: false,
          buttons: organisers,
          maxSelected: organisers.length,
          onSelected: (val, i, selected) {
            debugPrint('Button: $val index: $i selected: $selected');
            debugPrint(
                controller.organiserController.selectedIndexes.toString());
          },
          options: GroupButtonOptions(
            selectedShadow: const [],
            selectedTextStyle: const TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
            selectedColor: Colors.grey[500],
            unselectedShadow: const [],
            unselectedColor: Colors.grey[200],
            unselectedTextStyle: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
            selectedBorderColor: Colors.grey[200],
            unselectedBorderColor: Colors.grey[200],
            borderRadius: BorderRadius.circular(10),
            spacing: 10,
            runSpacing: 10,
            groupingType: GroupingType.wrap,
            direction: Axis.horizontal,
            //buttonHeight: 60,
            //buttonWidth: 60,
            mainGroupAlignment: MainGroupAlignment.start,
            crossGroupAlignment: CrossGroupAlignment.start,
            groupRunAlignment: GroupRunAlignment.start,
            textAlign: TextAlign.center,
            textPadding: EdgeInsets.zero,
            alignment: Alignment.center,
            elevation: 0,
          ),
        ),
      ),
      const Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.0),
        child: Text("Month"),
      ),
      Padding(
        padding: const EdgeInsets.all(10.0),
        child: GroupButton(
          controller: controller.monthController,
          isRadio: false,
          buttons: months,
          maxSelected: months.length,
          onSelected: (val, i, selected) {
            debugPrint('Button: $val index: $i selected: $selected');
            debugPrint(controller.monthController.selectedIndexes.toString());
          },
          options: GroupButtonOptions(
            selectedShadow: const [],
            selectedTextStyle: const TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
            selectedColor: Colors.grey[500],
            unselectedShadow: const [],
            unselectedColor: Colors.grey[200],
            unselectedTextStyle: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
            selectedBorderColor: Colors.grey[200],
            unselectedBorderColor: Colors.grey[200],
            borderRadius: BorderRadius.circular(10),
            spacing: 10,
            runSpacing: 10,
            groupingType: GroupingType.wrap,
            direction: Axis.horizontal,
            //buttonHeight: 60,
            //buttonWidth: 60,
            mainGroupAlignment: MainGroupAlignment.start,
            crossGroupAlignment: CrossGroupAlignment.start,
            groupRunAlignment: GroupRunAlignment.start,
            textAlign: TextAlign.center,
            textPadding: EdgeInsets.zero,
            alignment: Alignment.center,
            elevation: 0,
          ),
        ),
      ),
      Padding(
        padding:
            const EdgeInsets.only(left: 50.0, top: 20, right: 50, bottom: 20),
        child: ElevatedButton(
            onPressed: () async {
              filterDrawerKey.currentState!.closeEndDrawer();
              controller.filterDataAndReload();
            },
            style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                backgroundColor:
                    WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.0),
                        side: const BorderSide(
                            color: AppColor.primaryButtonColor)))),
            child: Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              height: 50,
              child: const Center(
                child: Text("Apply Filter", style: TextStyle(fontSize: 16)),
              ),
            )),
      ),
    ]);
  }
}
