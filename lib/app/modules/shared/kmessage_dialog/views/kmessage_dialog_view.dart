import 'package:automoment/app/constants/app_color.dart';
import 'package:flutter/material.dart';

class KMessageDialogView extends StatelessWidget {
  final String content;
  final Function? callback;

  const KMessageDialogView({super.key, required this.content, this.callback});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
        title: const Text("Automoment"),
        content: Text(content),
        actions: [
          TextButton(
            child: const Text(
              "OK",
              style: TextStyle(color: AppColor.secondaryColor),
            ),
            onPressed: () {
              if (callback == null) {
                Navigator.of(context).pop();
              } else {
                callback!();
              }
            },
          )
        ]);
  }
}
