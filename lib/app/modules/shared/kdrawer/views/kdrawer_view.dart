import 'package:automoment/app/controllers/user_controller.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import '../../../../constants/app_config.dart';
import '../../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../../bottombar/controllers/bottombar_controller.dart';
import '../controllers/kdrawer_controller.dart';

class KDrawerView extends StatelessWidget {
  KDrawerView({super.key});

  final KDrawerController controller = Get.put(KDrawerController());
  final UserController userController = Get.put(UserController());
  final BottombarController bottombarController =
      Get.put(BottombarController());

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        color: Colors.black87,
        child: Safe<PERSON>rea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView(
                  children: [
                    buildMenuItem(context, "News", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.changePage(1);
                    }),
                    buildMenuItem(context, "Events", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.changePage(2);
                    }),
                    buildMenuItem(context, "Results", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.changePage(3);
                    }),
                    buildMenuItem(context, "Reward", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.changePage(4);
                    }),
                    buildMenuItem(context, "Store", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.changePage(5);
                    }),
                    buildMenuItem(context, "Profile", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.changePage(6);
                    }),
                    buildMenuItem(context, "Chat", () {
                      Scaffold.of(context).closeDrawer();
                      bottombarController.chatPage();
                    }),
                    buildMenuItem(context, "Feedback / Bug report", () {
                      Scaffold.of(context).closeDrawer();

                      // show popup
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text("Feedback"),
                            content: const Text(
                                "We hope you're enjoying our app! If you have any feedback or suggestions, please don't hesitate to reach out to <NAME_EMAIL>."),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: const Text("Dismiss"),
                              ),
                              TextButton(
                                onPressed: () {
                                  final Uri emailLaunchUri = Uri(
                                    scheme: 'mailto',
                                    path: '<EMAIL>',
                                  );
                                  launchUrl(emailLaunchUri,
                                      mode: LaunchMode.platformDefault);
                                },
                                child: const Text("Send a feedback"),
                              ),
                            ],
                          );
                        },
                      );
                    }),
                    // (userController.isLoggedIn.value) ?
                    //   buildMenuItem(context, "Setting", () {}) :
                    //     const SizedBox(),
                  ],
                ),
              ),
              Obx(() {
                return (userController.isLoggedIn.value)
                    ? buildMenuItemLogout(context)
                    : buildMenuItemLogin(context);
              })
            ],
          ),
        ),
      ),
    );
  }

  Widget buildMenuItem(
      BuildContext context, String menuTitle, Function callback) {
    return InkWell(
      onTap: () {
        callback();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: AppConfig.defaultPadding * 2,
            vertical: AppConfig.defaultPadding),
        child: Text(
          menuTitle,
          style: const TextStyle(
              color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget buildMenuItemLogin(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            Scaffold.of(context).closeDrawer();
            GoRouter.of(context)
                .push(AppRoutePaths.guest); // Changed to GoRouter
          },
          child: const Padding(
            padding: EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding * 2,
                vertical: AppConfig.defaultPadding),
            child: Text(
              "Login / Register",
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(
            Icons.login,
            color: Colors.white,
          ),
          onPressed: () {},
        )
      ],
    );
  }

  Widget buildMenuItemLogout(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            //bottombarController.changePage(1);
            userController.logout();
            Scaffold.of(context).closeDrawer();
            GoRouter.of(context).go(AppRoutePaths.guest); // Changed to GoRouter
          },
          child: const Padding(
            padding: EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding * 2,
                vertical: AppConfig.defaultPadding),
            child: Text(
              "Logout",
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(
            Icons.logout,
            color: Colors.white,
          ),
          onPressed: () {},
        )
      ],
    );
  }
}
