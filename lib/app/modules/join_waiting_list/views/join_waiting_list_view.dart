import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../events_detail/controllers/events_detail_controller.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/join_waiting_list_controller.dart';

class JoinWaitingListView extends StatelessWidget {
  JoinWaitingListView({super.key});

  final JoinWaitingListController controller =
      Get.put(JoinWaitingListController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title:
              const Text('Join Waiting List', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(() {
          return (controller.vehicles.isNotEmpty)
              ? showBookingForm(context)
              : showNoVehicle(context);
        }));
  }

  showNoVehicle(BuildContext context) {
    if (!controller.isGetVehicleLoading.value) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('You have no vehicle registered',
                style: AppTextStyles.titleText),
            const SizedBox(height: 10),
            const Text('Please register a vehicle to book an event',
                style: AppTextStyles.normalText),
            const SizedBox(height: 20),
            ElevatedButton(
                onPressed: () async {
                  GoRouter.of(context)
                      .push(AppRoutePaths.vehiclesAdd, // Changed to GoRouter
                          extra: 1); // 1 is for booking
                },
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(
                        AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.primaryButtonColor)))),
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  width: 200,
                  child: const Center(
                    child: Text("Register Vehicle",
                        style: TextStyle(fontSize: 16)),
                  ),
                )),
          ],
        ),
      );
    }
    return Container();
  }

  showBookingForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Text(
              controller.event.value.title!,
              style: AppTextStyles.titleText,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Text(
              'Event Date: ${controller.showEventDate()}',
              style: AppTextStyles.normalText,
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 5),
            child: Text(
              'Please select your vehicle',
              style: AppTextStyles.normalText,
            ),
          ),
          Obx(() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (var index = 0; index < controller.vehicles.length; index++)
                  GestureDetector(
                    onTap: () {
                      controller.selectedVehicle.value =
                          controller.vehicles[index].id!;
                    },
                    child: Container(
                      margin: const EdgeInsets.only(top: 10),
                      child: Row(
                        children: [
                          Radio(
                            activeColor: AppColor.primaryColor,
                            value: controller.vehicles[index].id,
                            groupValue: controller.selectedVehicle.value,
                            onChanged: (value) {
                              controller.selectedVehicle.value = value as int;
                              controller.selectedIndex.value = index;
                            },
                          ),
                          // box for vehicle image and name
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: AppColor.primaryColor),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  // vehicle image
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: (controller.vehicles[index].image ==
                                            "")
                                        ? Image.asset(
                                            "assets/images/placeholder_vehicle.jpg",
                                            width: 150,
                                            height: 60,
                                            fit: BoxFit.fitWidth,
                                          )
                                        : Image.network(
                                            "${AppConfig.storageUrl}${controller.vehicles[index].image}",
                                            width: 100,
                                            height: 60,
                                            fit: BoxFit.fitWidth, errorBuilder:
                                                (context, error, stackTrace) {
                                            return Image.asset(
                                              "assets/images/placeholder_vehicle.jpg",
                                              width: 150,
                                              height: 60,
                                              fit: BoxFit.fitWidth,
                                            );
                                          }),
                                  ),

                                  const SizedBox(width: 10),
                                  // vehicle name
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "${controller.vehicles[index].make!} ${controller.vehicles[index].model!} ${controller.vehicles[index].year ?? ""}",
                                          style: AppTextStyles.normalTextBold,
                                        ),
                                        const SizedBox(height: 5),
                                        Text(
                                          controller.vehicles[index].type!,
                                          style: AppTextStyles.normalText,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            );
          }),
          const SizedBox(
            height: 30,
          ),
          ElevatedButton(
              onPressed: () async {
                if (controller.selectedVehicle.value == 0) {
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext dialogContext) {
                        return const KMessageDialogView(
                          content: "Please select your vehicle",
                        );
                      });
                } else {
                  if (controller.event.value.isRequireVehiclePlateNumber! ==
                      true) {
                    controller.plateNumberController.text = controller
                            .vehicles[controller.selectedIndex.value]
                            .plateNumber ??
                        "";
                    // show bottom sheet, with rounded corner on top, asking vehicle plate number
                    await showModalBottomSheet(
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        context: context,
                        isScrollControlled: true,
                        builder: (context) {
                          return Container(
                            padding: EdgeInsets.only(
                                bottom:
                                    MediaQuery.of(context).viewInsets.bottom),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(20),
                                  child: Text(
                                    "Please provide your vehicle's license plate number for this event.",
                                    style: AppTextStyles.normalText
                                        .copyWith(height: 1.5),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Container(
                                  width: 150,
                                  padding: const EdgeInsets.only(bottom: 20),
                                  child: TextFormField(
                                    controller:
                                        controller.plateNumberController,
                                    style: AppTextStyles.normalText.copyWith(
                                      fontSize: 20,
                                    ),
                                    textAlign: TextAlign.center,
                                    decoration: const InputDecoration(
                                      hintText: "Plate Number",
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 20, right: 20, bottom: 20),
                                  child: ElevatedButton(
                                      onPressed: () async {
                                        if (controller.plateNumberController
                                            .text.isEmpty) {
                                          showDialog(
                                              context: context,
                                              barrierDismissible: false,
                                              builder:
                                                  (BuildContext dialogContext) {
                                                return const KMessageDialogView(
                                                  content:
                                                      "Please enter your vehicle's license plate number",
                                                );
                                              });
                                        } else {
                                          if (await controller.bookEvent()) {
                                            Navigator.of(context)
                                                .pop(); // Pops the modal bottom sheet
                                            EventsDetailController
                                                eventDetailController =
                                                Get.find();
                                            eventDetailController
                                                .fetchCheckBookingStatus();

                                            // show thank you for joining message
                                            showDialog(
                                                context:
                                                    context, // IMPORTANT: This 'context' is from the modal sheet. It will be invalid after the sheet is popped.
                                                // TODO: This dialog should be shown using the page's context AFTER the sheet is dismissed.
                                                barrierDismissible: false,
                                                builder: (BuildContext
                                                    dialogContext) {
                                                  return KMessageDialogView(
                                                    content: controller.msg,
                                                    callback: () {
                                                      Navigator.of(
                                                              dialogContext)
                                                          .pop(); // Dismiss this dialog
                                                      // TODO: Ensure 'context' for GoRouter.of(context).pop() is the page's context.
                                                      GoRouter.of(context)
                                                          .pop();
                                                      GoRouter.of(context)
                                                          .pop();
                                                    },
                                                  );
                                                });
                                          } else {
                                            showDialog(
                                                context:
                                                    context, // IMPORTANT: This 'context' is from the modal sheet. Potentially invalid after sheet pop.
                                                // TODO: This dialog should be shown using the page's context.
                                                barrierDismissible: false,
                                                builder: (BuildContext
                                                    dialogContext) {
                                                  return KMessageDialogView(
                                                      content: controller.msg);
                                                });
                                          }
                                        }
                                      },
                                      style: ButtonStyle(
                                          foregroundColor:
                                              WidgetStateProperty.all<Color>(
                                                  Colors.white),
                                          backgroundColor:
                                              WidgetStateProperty.all<Color>(
                                                  AppColor.primaryButtonColor),
                                          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                              RoundedRectangleBorder(
                                                  borderRadius: BorderRadius.circular(
                                                      18.0),
                                                  side: const BorderSide(
                                                      color: AppColor
                                                          .primaryButtonColor)))),
                                      child: Container(
                                        padding: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        height: 50,
                                        width: 200,
                                        child: const Center(
                                          child: Text("Join Waiting List",
                                              style: TextStyle(fontSize: 16)),
                                        ),
                                      )),
                                ),
                              ],
                            ),
                          );
                        });
                  } else {
                    // submit booking
                    if (await controller.bookEvent()) {
                      EventsDetailController eventDetailController = Get.find();
                      eventDetailController.fetchCheckBookingStatus();

                      // show thank you for joining message
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return KMessageDialogView(
                            content: controller.msg,
                            callback: () {
                              Navigator.of(context).pop();
                              Navigator.of(context).pop();
                            },
                          );
                        },
                      );
                    } else {
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return KMessageDialogView(
                            content: controller.msg,
                            callback: () {
                              Navigator.of(context).pop();
                              Navigator.of(context).pop();
                            },
                          );
                        },
                      );
                    }
                  }
                }
              },
              style: ButtonStyle(
                  foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                  backgroundColor: WidgetStateProperty.all<Color>(
                      AppColor.primaryButtonColor),
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(18.0),
                          side: const BorderSide(
                              color: AppColor.primaryButtonColor)))),
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                height: 50,
                child: const Center(
                  child:
                      Text("Join Waiting List", style: TextStyle(fontSize: 16)),
                ),
              )),
        ],
      ),
    );
  }
}
