import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import '../../../constants/app_color.dart';
import '../../../models/product_model.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../controllers/store_controller.dart';

class ProductsView extends StatelessWidget {
  ProductsView({super.key});

  final StoreController controller = Get.put(StoreController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return GridView.builder(
        padding: const EdgeInsets.all(10),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // Two columns
          childAspectRatio: 0.72, // Adjust aspect ratio for desired look
          mainAxisSpacing: 10,
          crossAxisSpacing: 10,
        ),
        itemCount: controller.productList.length,
        itemBuilder: (context, index) {
          // Pass context to productItem
          return productItem(context, controller.productList[index]);
        },
      );
    });
  }

  // Modified to accept BuildContext
  Widget productItem(BuildContext context, Product product) {
    return GestureDetector(
      onTap: () {
        GoRouter.of(context).push(AppRoutePaths.productDetail,
            extra: product.id); // Changed to GoRouter
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            child: Image.network(
              product.featuredImage ?? "",
              height: 160,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name ?? "",
                  style: AppTextStyles.normalTextBold.copyWith(
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 5),
                if (product.priceSale != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'SGD ${product.priceSale}',
                        style: AppTextStyles.normalTextBold.copyWith(
                          fontSize: 12,
                          color: AppColor.redButtonColor,
                        ),
                      ),
                      const TextSpan(text: '  '),
                      TextSpan(
                        text: "SGD ${product.price}",
                        style: AppTextStyles.normalText.copyWith(
                          decoration: TextDecoration.lineThrough,
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ]),
                  )
                else
                  Text(
                    'SGD ${product.price}',
                    style: AppTextStyles.normalText.copyWith(
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
