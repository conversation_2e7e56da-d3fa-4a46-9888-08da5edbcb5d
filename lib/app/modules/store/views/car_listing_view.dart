import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart'; // Keep for controller
import 'package:go_router/go_router.dart'; // Add GoRouter import

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/car_listing_model.dart';
// import '../../../routes/app_pages.dart'; // Remove GetX routes
import '../../../routes/app_router.dart'; // Import AppRoutePaths
import '../controllers/store_controller.dart';

class CarListingsView extends StatelessWidget {
  CarListingsView({super.key});

  final StoreController controller = Get.put(StoreController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return GridView.builder(
        padding: const EdgeInsets.all(10),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // Two columns
          childAspectRatio: 0.7, // Adjust aspect ratio for desired look
          mainAxisSpacing: 10,
          crossAxisSpacing: 10,
        ),
        itemCount: controller.carListingList.length,
        itemBuilder: (context, index) {
          return carListingItem(controller.carListingList[index]);
        },
      );
    });
  }

  Widget carListingItem(CarListing carListing) {
    bool isForRent = carListing.isForRent!;
    bool isForSale = carListing.isForSale!;
    String currencyRent = "SGD";
    String currencySell = "SGD";
    int? priceRent = carListing.priceRent;
    int? priceSell = carListing.priceSell;

    if (carListing.priceRent == null && carListing.priceRentMyr != null) {
      currencyRent = "MYR";
      priceRent = carListing.priceRentMyr;
    } else if (carListing.priceSell == null &&
        carListing.priceSellMyr != null) {
      currencySell = "MYR";
      priceSell = carListing.priceSellMyr;
    }

    if (carListing.priceRent == null && carListing.priceRentMyr == null) {
      isForRent = false;
    }

    if (carListing.priceSell == null && carListing.priceSellMyr == null) {
      isForSale = false;
    }

    return GestureDetector(
      onTap: () {
        GoRouter.of(Get.context!)
            .push(AppRoutePaths.carListingDetail, extra: carListing.id);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            child: Image.network(
              carListing.featuredImage ?? "",
              height: 160,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  carListing.name ?? "",
                  style: AppTextStyles.normalTextBold.copyWith(
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 5),
                if (isForRent)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // make text with background yellow, text color red, rounded
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 5,
                        ),
                        decoration: BoxDecoration(
                          color: AppColor.redButtonColor,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Text('For Rent',
                            style: AppTextStyles.normalText.copyWith(
                              fontSize: 12,
                              color: Colors.white,
                            )),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(
                        '$currencyRent ${StringUtil.formatMoneyWithoutCent(priceRent!.toDouble())}/day',
                        style: AppTextStyles.normalText.copyWith(
                          fontSize: 12,
                          color: AppColor.redButtonColor,
                        ),
                      ),
                    ],
                  ),
                if (isForRent) const SizedBox(height: 5),
                if (isForSale)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 5,
                        ),
                        decoration: BoxDecoration(
                          color: AppColor.secondaryColor,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Text('For Sale',
                            style: AppTextStyles.normalText.copyWith(
                              fontSize: 12,
                              color: Colors.white,
                            )),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        '$currencySell ${StringUtil.formatMoneyWithoutCent(priceSell!.toDouble())}',
                        style: AppTextStyles.normalText.copyWith(
                          fontSize: 12,
                          color: AppColor.redButtonColor,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
