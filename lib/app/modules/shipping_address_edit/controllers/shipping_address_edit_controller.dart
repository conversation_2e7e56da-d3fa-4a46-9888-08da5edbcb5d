import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import
import '../../../controllers/user_controller.dart';
import '../../../helpers/debug_helper.dart';
import '../../../services/api_client.dart';
import '../../shipping_address/controllers/shipping_address_controller.dart';

class ShippingAddressEditController extends BaseScreenController {
  @override
  String get screenName => 'Shipping Address Edit';

  final formKey = GlobalKey<FormState>();
  final userController = Get.find<UserController>();

  final fullNameController = TextEditingController();
  final phoneController = TextEditingController();
  final addressLine1Controller = TextEditingController();
  final addressLine2Controller = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final postalCodeController = TextEditingController();
  final selectedCountry = 'Singapore'.obs;

  final isLoading = false.obs;
  late final Map<String, dynamic> addressData;
  late final int addressId;

  @override
  void onInit() {
    super.onInit();
    addressData = Get.arguments as Map<String, dynamic>;
    addressId = addressData['id'];
    _loadAddressData();
  }

  void _loadAddressData() {
    fullNameController.text = addressData['full_name'] ?? '';
    phoneController.text = addressData['phone'] ?? '';
    addressLine1Controller.text = addressData['address_line1'] ?? '';
    addressLine2Controller.text = addressData['address_line2'] ?? '';
    cityController.text = addressData['city'] ?? '';
    stateController.text = addressData['state'] ?? '';
    postalCodeController.text = addressData['postal_code'] ?? '';
    selectedCountry.value = addressData['country'] ?? 'Singapore';
  }

  @override
  void onClose() {
    fullNameController.dispose();
    phoneController.dispose();
    addressLine1Controller.dispose();
    addressLine2Controller.dispose();
    cityController.dispose();
    stateController.dispose();
    postalCodeController.dispose();
    super.onClose();
  }

  Future<void> updateAddress() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isLoading.value = true;

      final address = {
        'full_name': fullNameController.text,
        'phone': phoneController.text,
        'address_line1': addressLine1Controller.text,
        'address_line2': addressLine2Controller.text.isNotEmpty
            ? addressLine2Controller.text
            : null,
        'city': cityController.text.isNotEmpty ? cityController.text : null,
        'state': stateController.text.isNotEmpty ? stateController.text : null,
        'postal_code': postalCodeController.text.isNotEmpty
            ? postalCodeController.text
            : null,
        'country': selectedCountry.value,
        'is_default': addressData['is_default'] ?? false,
      };

      final response = await ApiClient.updateShippingAddress(
        userController.getToken(),
        addressId,
        address,
      );

      if (response['success'] == true) {
        if (Get.context != null) {
          GoRouter.of(Get.context!)
              .pop(true); // Changed to GoRouter.of(Get.context!).pop
        }
        Get.find<ShippingAddressController>().fetchAddresses();
        SnackHelper.showSuccess('Address updated successfully');
      }
    } catch (e) {
      DebugHelper.d('Error updating address: $e');
      SnackHelper.showError('Failed to update address');
    } finally {
      isLoading.value = false;
    }
  }
}
