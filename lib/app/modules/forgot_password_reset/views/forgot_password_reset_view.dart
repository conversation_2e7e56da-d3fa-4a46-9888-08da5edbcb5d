import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/forgot_password_reset_controller.dart';

class ForgotPasswordResetView extends StatefulWidget {
  const ForgotPasswordResetView({super.key});

  @override
  State<ForgotPasswordResetView> createState() =>
      _ForgotPasswordResetViewState();
}

class _ForgotPasswordResetViewState extends State<ForgotPasswordResetView> {
  final ForgotPasswordResetController controller =
      Get.put(ForgotPasswordResetController());

  @override
  void dispose() {
    Get.delete<ForgotPasswordResetController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Reset Password Request',
            style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Form(
          key: controller.formKey,
          child: ListView(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Text(
                  'Please enter the new password',
                  style: AppTextStyles.normalText,
                ),
              ),
              TextFormField(
                controller: controller.passwordController,
                keyboardType: TextInputType.text,
                obscureText: true,
                autocorrect: false,
                decoration: const InputDecoration(
                  labelText: "Password",
                  hintText: "",
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: controller.passwordConfirmController,
                keyboardType: TextInputType.text,
                obscureText: true,
                autocorrect: false,
                decoration: const InputDecoration(
                  labelText: "Confirm Password",
                  hintText: "",
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required';
                  }
                  return null;
                },
              ),
              const SizedBox(
                height: 30,
              ),
              // black button
              ElevatedButton(
                  onPressed: () async {
                    if (controller.formKey.currentState!.validate()) {
                      if (controller.passwordController.value.text !=
                          controller.passwordConfirmController.value.text) {
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return const KMessageDialogView(
                              content: "Incorrect password confirmation.",
                            );
                          },
                        );
                      } else {
                        if (await controller.resetPassword(controller.code,
                            controller.passwordController.value.text)) {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return KMessageDialogView(
                                content: controller.msg,
                                callback: () {
                                  GoRouter.of(context).go(AppRoutePaths.guest);
                                },
                              );
                            },
                          );
                        } else {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return KMessageDialogView(
                                  content: controller.msg);
                            },
                          );
                        }
                      }
                    }
                  },
                  style: ButtonStyle(
                      foregroundColor:
                          WidgetStateProperty.all<Color>(Colors.white),
                      backgroundColor: WidgetStateProperty.all<Color>(
                          AppColor.primaryButtonColor),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                              side: const BorderSide(
                                  color: AppColor.primaryButtonColor)))),
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 50,
                    child: const Center(
                      child: Text("Reset Password",
                          style: TextStyle(fontSize: 16)),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
