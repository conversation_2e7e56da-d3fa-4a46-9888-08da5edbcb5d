import 'dart:convert';
import 'dart:typed_data';

import 'package:automoment/app/routes/app_router.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:signature/signature.dart';

import '../../../../form/checklist_model.dart';
import '../../../../form/form_widget.dart';
import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/loading_button.dart';
import '../../shared/support_button.dart';
import '../controllers/events_form_sign_controller.dart';

class EventsFormSignView extends GetView<EventsFormSignController> {
  EventsFormSignView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Indemnity Form', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Stack(
        children: [
          widgetFormBuilder(context),
          widgetOverlay(),
          widgetSignatureForm(context),
        ],
      ),
    );
  }

  widgetSignatureForm(BuildContext context) {
    return Obx(() {
      return (controller.isFormFilled.value)
          ? Center(
              child: Container(
                height: 310,
                width: 300,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      "Please sign this document.",
                      style:
                          AppTextStyles.normalTextBold.copyWith(fontSize: 16),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      width: 300,
                      height: 197,
                      child: Signature(
                        key: const Key('signature'),
                        controller: signatureController,
                        height: 200,
                        width: 280,
                        backgroundColor: Colors.grey[200]!,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                              bottom: AppConfig.defaultPadding,
                              left: AppConfig.defaultPadding,
                              right: AppConfig.defaultPadding / 2),
                          child: ElevatedButton(
                              onPressed: () async {
                                signatureController.clear();
                              },
                              style: ButtonStyle(
                                  foregroundColor:
                                      WidgetStateProperty.all<Color>(
                                          Colors.white),
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColor.primaryButtonColor),
                                  shape: WidgetStateProperty.all<
                                          RoundedRectangleBorder>(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(18.0),
                                          side: const BorderSide(
                                              color: AppColor
                                                  .primaryButtonColor)))),
                              child: Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                height: 50,
                                child: const Center(
                                  child: Text("Clear",
                                      style: TextStyle(fontSize: 16)),
                                ),
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              bottom: AppConfig.defaultPadding,
                              left: AppConfig.defaultPadding / 2,
                              right: AppConfig.defaultPadding),
                          child: (controller.isLoading.value)
                              ? loadingButton("Next")
                              : ElevatedButton(
                                  onPressed: () async {
                                    await exportImage(context);

                                    if (await controller.submitForm()) {
                                      GoRouter.of(context).push(
                                          AppRoutePaths.eventsFormSignSuccess,
                                          extra: controller.event.value);
                                    } else {
                                      // Using GoRouter's dialog method
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (context) => KMessageDialogView(
                                            content: controller.msg),
                                      );
                                    }
                                  },
                                  style: ButtonStyle(
                                      foregroundColor: WidgetStateProperty.all<Color>(
                                          Colors.white),
                                      backgroundColor:
                                          WidgetStateProperty.all<Color>(
                                              AppColor.primaryButtonColor),
                                      shape: WidgetStateProperty.all<
                                              RoundedRectangleBorder>(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(18.0),
                                              side: const BorderSide(
                                                  color: AppColor
                                                      .primaryButtonColor)))),
                                  child: Container(
                                    padding: const EdgeInsets.only(
                                        left: 20, right: 20),
                                    height: 50,
                                    child: const Center(
                                      child: Text("Next",
                                          style: TextStyle(fontSize: 16)),
                                    ),
                                  )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )
          : const SizedBox();
    });
  }

  final SignatureController signatureController = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
    exportPenColor: Colors.black,
    onDrawStart: () => debugPrint('onDrawStart called!'),
    onDrawEnd: () => debugPrint('onDrawEnd called!'),
  );

  widgetFormBuilder(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(() {
        return (!controller.isLoading.value)
            ? Column(
                children: [
                  FormBuilder(
                    initialData: controller.formJson,
                    index: 0,
                    showIndex: false,
                    submitButtonWidth: 1,
                    submitTextDecoration:
                        const TextStyle(fontSize: 16, color: Colors.white),
                    submitButtonDecoration: BoxDecoration(
                      color: AppColor.primaryButtonColor,
                      borderRadius: BorderRadius.circular(18),
                    ),
                    showIcon: false,
                    onSubmit: (ChecklistModel val) async {
                      var json = jsonEncode(val.toJson());
                      debugPrint(json);

                      controller.jsonForm = json;
                      controller.isFormFilled.value = true;
                    },
                  )
                ],
              )
            : Container();
      }),
    );
  }

  widgetOverlay() {
    return Obx(() {
      return (controller.isFormFilled.value)
          ? Container(
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(26 * 7),
              ),
            )
          : const SizedBox();
    });
  }

  Future<void> exportImage(BuildContext context) async {
    final Uint8List? data =
        await signatureController.toPngBytes(width: 280, height: 200);

    String base64Image = base64Encode(data!);
    debugPrint("base64Image: $base64Image");

    controller.signatureBase64 = base64Image;
  }
}
