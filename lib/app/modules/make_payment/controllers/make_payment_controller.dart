import 'dart:convert';
import 'dart:io';

import 'package:automoment/app/helpers/debug_helper.dart';
import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:automoment/app/helpers/string_util.dart';
import 'package:automoment/app/modules/reward/controllers/reward_controller.dart';
import 'package:automoment/app/modules/shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import 'package:automoment/app/modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import 'package:automoment/gallery_saver/lib/gallery_saver.dart';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../constants/app_config.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';
import '../../events_detail/controllers/events_detail_controller.dart';

class MakePaymentController extends BaseScreenController {
  @override
  String get screenName => 'Make Payment';

  var qty = 0.obs;
  var event = Event().obs;
  var discountedPrice = 0.0.obs;
  var totalAmount = 0.0.obs;
  var isCouponApplied = false.obs;
  var isAdditionalDriver = false.obs;
  var isAdditionalDriverAvailable = false.obs;
  var additionalDriverFee = 0.0.obs;
  var eventPrice = 0.0.obs;
  var discount = 0.0.obs;
  var discountMyr = 0.0.obs;

  // Track if the controller is mounted
  bool isMounted = true;

  var currency = "SGD".obs;
  var currencyPrefix = "SGD".obs;

  String msg = "";
  var isLoading = false.obs;
  final UserController userController = Get.put(UserController());
  final EventsDetailController eventDetailController =
      Get.put(EventsDetailController());
  final RewardController rewardController = Get.put(RewardController());

  late CreatePaymentIntentResult paymentIntentData;

  var isPaymentSuccess = false.obs;
  var payNowId = 0.obs;
  late WebViewController webViewController;

  var couponCodeController = TextEditingController();
  var type = "driver".obs;
  var registrationItemTitle = "".obs;

  final _walletBalance = '0.00'.obs;
  final _isLoadingBalance = false.obs;

  bool get isLoadingBalance => _isLoadingBalance.value;

  String get walletBalance => _walletBalance.value;

  bool get hasWalletBalance {
    try {
      final balance = double.parse(_walletBalance.value);
      return balance > 0;
    } catch (e) {
      return false;
    }
  }

  Future<void> fetchWalletBalance() async {
    try {
      _isLoadingBalance.value = true;
      final response =
          await ApiClient.getWalletBalance(userController.getToken());
      final jsonResponse = jsonDecode(response);
      if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
        _walletBalance.value = jsonResponse['data']['balance'];
      }
    } catch (e) {
      debugPrint('Error fetching wallet balance: $e');
    } finally {
      _isLoadingBalance.value = false;
    }
  }

  Future<void> payWithCredit() async {
    showDialog(
      context: Get.context!,
      builder: (BuildContext context) => KConfirmDialogView(
        content: 'Are you sure you want to pay using your credit balance?',
        callbackYes: () async {
          Navigator.of(context).pop(); // Close dialog
          try {
            EasyLoading.show(status: 'Processing payment...');
            final response = await ApiClient.payWithCredit(
                userController.getToken(),
                event.value.id!,
                type.value,
                couponCodeController.text);

            final jsonResponse = jsonDecode(response);
            if (jsonResponse['success'] == true) {
              _walletBalance.value = jsonResponse['data']['remaining_balance'];
              isPaymentSuccess.value = true;
              SnackHelper.showSuccess(
                  jsonResponse['message'] ?? 'Payment successful');
            } else {
              SnackHelper.showError(
                  jsonResponse['message'] ?? 'Payment failed');
            }
          } catch (e) {
            debugPrint('Error processing credit payment: $e');
            SnackHelper.showError('Failed to process credit payment');
          } finally {
            EasyLoading.dismiss();
          }
        },
        callbackNo: () {
          Navigator.of(context).pop(); // Close dialog
        },
      ),
    );
  }

  Future<void> refreshWalletBalance() async {
    await fetchWalletBalance();
  }

  var groupId = 0.obs;

  @override
  void onInit() {
    super.onInit();

    initWebViewController();

    fetchWalletBalance();

    if (Get.arguments is Event) {
      event.value = Get.arguments;
    } else {
      event.value = Get.arguments['event'];
      type.value = Get.arguments['type'];

      if (type.value == "group") {
        qty.value = Get.arguments['qty'];
        groupId.value = Get.arguments['groupId'];
      }
    }

    debugPrint("type: ${type.value}, "
        "event: ${event.value.title}, "
        "additionalDriverPrice: ${event.value.additionalDriverPrice}"
        "additionalDriverPriceMyr: ${event.value.additionalDriverPriceMyr}");

    currency.value = event.value.defaultCurrency!;

    setupPricing();

    if (type.value == "driver") {
      registrationItemTitle.value = "Driver fee";
    } else if (type.value == "passenger") {
      registrationItemTitle.value = "Passenger fee";
    } else if (type.value == "additional driver") {
      registrationItemTitle.value = "Additional driver fee";
    } else if (type.value == "group") {
      registrationItemTitle.value = "Driver fee";
    }

    isAdditionalDriverAvailable.value = event.value.additionalDriver!;

    Stripe.publishableKey = AppConfig.stripePublishableKey;
    //Stripe.merchantIdentifier = AppConfig.stripeMerchantIdentifier;
    Stripe.instance.applySettings();
  }

  initWebViewController() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {
            DebugHelper.d("onPageStarted: $url");
            EasyLoading.show(status: 'Please wait...');
          },
          onPageFinished: (String url) async {
            DebugHelper.d("onPageFinished: $url");

            if (url == "${AppConfig.webUrl}paynow/return/success") {
              // delay 5 second
              //EasyLoading.show(status: 'Please wait...');
              DebugHelper.d('start delay');
              await Future.delayed(const Duration(seconds: 5), () {
                DebugHelper.d('delay 5 second');
                Navigator.of(Get.context!).pop();
              });
              DebugHelper.d('finish delay');
              await checkBookingStatus();
              EasyLoading.dismiss();
            } else {
              EasyLoading.dismiss();
            }
          },
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            DebugHelper.d("navigationDelegate: ${request.url}");

            if (Platform.isAndroid) {
              if (request.url.contains("https://qr.stripe.com") &&
                  request.url.contains("download=true")) {
                GallerySaver.saveImage(request.url).then((bool? success) {
                  showDialog(
                      context: Get.context!,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return KMessageDialogView(
                            content:
                                "The QR code has been saved to your gallery. You can also access it with File Manager > Pictures.",
                            callback: () {
                              Navigator.of(context).pop();
                            });
                      });
                });
                return NavigationDecision.prevent;
              }
            }

            return NavigationDecision.navigate;
          },
        ),
      );
  }

  setupPricing() {
    if (currency.value == "SGD") {
      eventPrice.value = event.value.price!;
      discountedPrice.value = event.value.price!;
      totalAmount.value = event.value.price!;
      additionalDriverFee.value = event.value.additionalDriverPrice!;
      currencyPrefix.value = "SGD";

      if (type.value == "passenger") {
        eventPrice.value = event.value.passengerPrice!;
        discountedPrice.value = event.value.passengerPrice!;
        totalAmount.value = event.value.passengerPrice!;
      } else if (type.value == "additional driver") {
        eventPrice.value = event.value.additionalDriverPrice!;
        discountedPrice.value = event.value.additionalDriverPrice!;
        totalAmount.value = event.value.additionalDriverPrice!;
      } else if (type.value == "group") {
        eventPrice.value = event.value.price! * qty.value;
        totalAmount.value = event.value.price! * qty.value;
        updateTotalAmount();
      }
    } else if (currency.value == "MYR") {
      eventPrice.value = event.value.priceMyr!;
      discountedPrice.value = event.value.priceMyr!;
      totalAmount.value = event.value.priceMyr!;
      additionalDriverFee.value = event.value.additionalDriverPriceMyr!;
      currencyPrefix.value = "RM";

      if (type.value == "passenger") {
        eventPrice.value = event.value.passengerPriceMyr!;
        discountedPrice.value = event.value.passengerPriceMyr!;
        totalAmount.value = event.value.passengerPriceMyr!;
      } else if (type.value == "additional driver") {
        eventPrice.value = event.value.additionalDriverPriceMyr!;
        discountedPrice.value = event.value.additionalDriverPriceMyr!;
        totalAmount.value = event.value.additionalDriverPriceMyr!;
      } else if (type.value == "group") {
        eventPrice.value = event.value.priceMyr! * qty.value;
        totalAmount.value = event.value.priceMyr! * qty.value;
        updateTotalAmount();
      }
    }

    updateTotalAmount();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  @override
  void onClose() {
    // Set isMounted to false when the controller is disposed
    isMounted = false;
    super.onClose();
  }

  Future<bool> createPaymentIntent() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.stripePayment(
          userController.getToken(),
          event.value.id!,
          couponCodeController.text,
          isAdditionalDriver.value,
          currency.value);
      //msg = jsonDecode(response)['message'];

      paymentIntentData = CreatePaymentIntentResult(
        clientSecret: jsonDecode(response)['client_secret'],
        ephemeralKey: jsonDecode(response)['ephemeral_key'],
        customerId: jsonDecode(response)['customer_id'],
      );

      isLoading.value = false;
      EasyLoading.dismiss();
      return true; // jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<bool> createPaymentIntentStripePassenger() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.stripePaymentPassenger(
          userController.getToken(),
          event.value.id!,
          couponCodeController.text,
          currency.value);
      //msg = jsonDecode(response)['message'];

      paymentIntentData = CreatePaymentIntentResult(
        clientSecret: jsonDecode(response)['client_secret'],
        ephemeralKey: jsonDecode(response)['ephemeral_key'],
        customerId: jsonDecode(response)['customer_id'],
      );

      isLoading.value = false;
      EasyLoading.dismiss();
      return true; // jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<bool> createPaymentIntentStripeAdditionalDriver() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.stripePaymentAdditionalDriver(
          userController.getToken(),
          event.value.id!,
          couponCodeController.text,
          currency.value);

      paymentIntentData = CreatePaymentIntentResult(
        clientSecret: jsonDecode(response)['client_secret'],
        ephemeralKey: jsonDecode(response)['ephemeral_key'],
        customerId: jsonDecode(response)['customer_id'],
      );

      isLoading.value = false;
      EasyLoading.dismiss();
      return true; // jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<bool> createPaymentIntentStripeGroup() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.stripePaymentGroup(
          userController.getToken(),
          event.value.id!,
          groupId.value,
          couponCodeController.text,
          currency.value);

      paymentIntentData = CreatePaymentIntentResult(
        clientSecret: jsonDecode(response)['client_secret'],
        ephemeralKey: jsonDecode(response)['ephemeral_key'],
        customerId: jsonDecode(response)['customer_id'],
      );

      isLoading.value = false;
      EasyLoading.dismiss();
      return true; // jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<void> freeBooking() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.freeBooking(
          userController.getToken(),
          event.value.id!,
          couponCodeController.text,
          isAdditionalDriver.value,
          currency.value);

      isLoading.value = false;
      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'];
      msg = jsonDecode(response)['msg'];

      if (success) {
        isPaymentSuccess.value = true;
        updateBookingStatus();
        rewardController.fetchRewards();
      } else {
        SnackHelper.showError(msg);
      }
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<void> freeBookingForAdditionalDriver() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.freeBookingForAdditionalDriver(
          userController.getToken(), event.value.id!);

      isLoading.value = false;
      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      if (success) {
        isPaymentSuccess.value = true;
        updateBookingStatus();
        rewardController.fetchRewards();
      } else {
        SnackHelper.showError(msg);
      }
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<int> createPaymentIntentPayNow() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.payNowPayment(userController.getToken(),
          event.value.id!, couponCodeController.text, isAdditionalDriver.value);

      isLoading.value = false;
      EasyLoading.dismiss();
      return jsonDecode(response)['payment_id'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<int> createPaymentIntentPayNowPassenger() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.payNowPaymentPassenger(
          userController.getToken(),
          event.value.id!,
          couponCodeController.text);

      isLoading.value = false;
      EasyLoading.dismiss();
      return jsonDecode(response)['payment_id'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<int> createPaymentIntentPayNowAdditionalDriver() async {
    try {
      isLoading.value = true;
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.payNowPaymentAdditionalDriver(
          userController.getToken(),
          event.value.id!,
          couponCodeController.text);
      isLoading.value = false;
      EasyLoading.dismiss();
      return jsonDecode(response)['payment_id'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<int> createPaymentIntentPayNowGroup() async {
    try {
      isLoading.value = true;
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.payNowPaymentGroup(
          userController.getToken(),
          event.value.id!,
          groupId.value,
          couponCodeController.text);
      isLoading.value = false;
      EasyLoading.dismiss();
      return jsonDecode(response)['payment_id'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  calculateAmount(String amount) {
    final a = (int.parse(amount)) * 100;
    return a.toString();
  }

  displayPaymentSheet() async {
    try {
      await Stripe.instance.presentPaymentSheet();
      isPaymentSuccess.value = true;
      updateBookingStatus();
      rewardController.fetchRewards();

      SnackHelper.showSuccess('Payment successful');
    } on Exception catch (e) {
      if (e is StripeException) {
        debugPrint("Error from Stripe: ${e.error.localizedMessage}");
      } else {
        debugPrint("Unforeseen error: $e");
      }
    } catch (e) {
      debugPrint("exception:$e");
    }
  }

  Future<void> makePayment() async {
    try {
      if (type.value == "driver") {
        await createPaymentIntent();
      } else if (type.value == "passenger") {
        await createPaymentIntentStripePassenger();
      } else if (type.value == "additional driver") {
        await createPaymentIntentStripeAdditionalDriver();
      } else if (type.value == "group") {
        await createPaymentIntentStripeGroup();
      }

      debugPrint("customerId: ${paymentIntentData.customerId}");
      debugPrint("clientSecret: ${paymentIntentData.clientSecret}");
      debugPrint("ephemeralKey: ${paymentIntentData.ephemeralKey}");

      await Stripe.instance.initPaymentSheet(
          paymentSheetParameters: SetupPaymentSheetParameters(
        merchantDisplayName: 'Automoment',
        customerId: paymentIntentData.customerId,
        paymentIntentClientSecret: paymentIntentData.clientSecret,
        customerEphemeralKeySecret: paymentIntentData.ephemeralKey,
        style: ThemeMode.light,
        allowsDelayedPaymentMethods: false,
        returnURL: 'automoment://payment', // Add return URL scheme
        //style: ThemeMode.dark,
        //customFlow: true,
        billingDetails: const BillingDetails(
          address: Address(
            city: 'Singapore',
            country: 'SG',
            line1: '',
            line2: '',
            state: 'Singapore',
            postalCode: '',
          ),
        ),
        //applePay: const PaymentSheetApplePay(
        //    merchantCountryCode: 'SG'
        //),
        // googlePay: PaymentSheetGooglePay(
        //     merchantCountryCode: 'SG',
        //     currencyCode: 'SGD',
        //     testEnv: AppConfig.googlePayTestEnvironment
        // ),
        //customFlow: true,
      ));

      displayPaymentSheet();
    } catch (e, s) {
      debugPrint('exception:$e$s');
    }
  }

  Future<void> makePaymentPayNow() async {
    try {
      if (type.value == "driver") {
        payNowId.value = await createPaymentIntentPayNow();
      } else if (type.value == "passenger") {
        payNowId.value = await createPaymentIntentPayNowPassenger();
      } else if (type.value == "additional driver") {
        payNowId.value = await createPaymentIntentPayNowAdditionalDriver();
      } else if (type.value == "group") {
        payNowId.value = await createPaymentIntentPayNowGroup();
      }

      debugPrint("paymentId: ${payNowId.value}");

      webViewController.loadRequest(Uri.parse(
          "${AppConfig.webUrl}paynow/${payNowId.value}?${StringUtil.randomString(10)}"));
      // open webview paynow
    } catch (e, s) {
      debugPrint('exception:$e$s');
    }
  }

  Future checkBookingStatus({int retryCount = 0}) async {
    debugPrint("checkBookingStatus retryCount: $retryCount");
    try {
      var response = await ApiClient.checkBookingStatus(
          userController.getToken(),
          userController.user.value.id!,
          event.value.id!);
      int bookingStatus = jsonDecode(response)['status'];
      debugPrint(
          "bookingStatus: $bookingStatus ${jsonDecode(response)['message']}");

      if (bookingStatus == 2 || bookingStatus == 7 || bookingStatus == 9) {
        isPaymentSuccess.value = true;
        updateBookingStatus();
        if (bookingStatus == 2) {
          rewardController.fetchRewards();
        }

        SnackHelper.showSuccess('Payment successful');
      } else {
        if (retryCount < 2) {
          // Retry after a short delay
          await Future.delayed(const Duration(seconds: 2));
          return checkBookingStatus(retryCount: retryCount + 1);
        } else {
          SnackHelper.showError('Payment cancelled');
        }
      }
    } catch (e) {
      if (retryCount < 2) {
        // Retry after a short delay
        await Future.delayed(const Duration(seconds: 2));
        return checkBookingStatus(retryCount: retryCount + 1);
      } else {
        throw Exception(e.toString());
      }
    }
  }

  updateBookingStatus() {
    eventDetailController.fetchCheckBookingStatusFromPaymentScreen();
  }

  applyCouponCode() async {
    var response = await ApiClient.checkDiscountCoupon(
        userController.getToken(), event.value.id!, couponCodeController.text);
    String status = jsonDecode(response)['status'];
    debugPrint(
        "checkDiscountCoupon discountedPrice: $status ${jsonDecode(response)['discountedPrice']}");

    if (status == 'success') {
      discount.value = jsonDecode(response)['discountedPrice'].toDouble();
      discountMyr.value = jsonDecode(response)['discountedPriceMyr'].toDouble();

      if (currency.value == 'SGD') {
        discountedPrice.value = discount.value;
      } else if (currency.value == 'MYR') {
        discountedPrice.value = discountMyr.value;
      }

      isCouponApplied.value = true;
      updateTotalAmount();
      SnackHelper.showSuccess('Coupon Applied');
    } else {
      discountedPrice.value = event.value.price!;
      isCouponApplied.value = false;
      updateTotalAmount();
      String msg = jsonDecode(response)['message'];
      SnackHelper.showError(msg);
    }
  }

  void updateTotalAmount() {
    if (isCouponApplied.value) {
      totalAmount.value = discountedPrice.value;
    } else {
      totalAmount.value = eventPrice.value;
    }
    if (isAdditionalDriver.value) {
      totalAmount.value = totalAmount.value + additionalDriverFee.value;
    }

    if (type.value == "group") {
      totalAmount.value = eventPrice.value;
    }
  }

  void switchCurrency() {
    if (currency.value == 'SGD') {
      currency.value = 'MYR';
      currencyPrefix.value = 'RM';
      eventPrice.value = event.value.priceMyr!;

      if (isCouponApplied.value) {
        discountedPrice.value = discountMyr.value;
      } else {
        discountedPrice.value = event.value.priceMyr!;
      }

      totalAmount.value = event.value.priceMyr!;
      additionalDriverFee.value = event.value.additionalDriverPriceMyr!;
      updateTotalAmount();

      if (type.value == "passenger") {
        eventPrice.value = event.value.passengerPriceMyr!;
        discountedPrice.value = event.value.passengerPriceMyr!;
        totalAmount.value = event.value.passengerPriceMyr!;
        updateTotalAmount();
      } else if (type.value == "additional driver") {
        eventPrice.value = event.value.additionalDriverPriceMyr!;
        discountedPrice.value = event.value.additionalDriverPriceMyr!;
        totalAmount.value = event.value.additionalDriverPriceMyr!;
        updateTotalAmount();
      } else if (type.value == "group") {
        eventPrice.value = event.value.priceMyr! * qty.value;
        totalAmount.value = event.value.priceMyr! * qty.value;
        updateTotalAmount();
      }
    } else {
      currency.value = 'SGD';
      currencyPrefix.value = 'SGD';
      eventPrice.value = event.value.price!;

      if (isCouponApplied.value) {
        discountedPrice.value = discount.value;
      } else {
        discountedPrice.value = event.value.price!;
      }

      totalAmount.value = event.value.price!;
      additionalDriverFee.value = event.value.additionalDriverPrice!;
      updateTotalAmount();

      if (type.value == "passenger") {
        eventPrice.value = event.value.passengerPrice!;
        discountedPrice.value = event.value.passengerPrice!;
        totalAmount.value = event.value.passengerPrice!;
        updateTotalAmount();
      } else if (type.value == "additional driver") {
        eventPrice.value = event.value.additionalDriverPrice!;
        discountedPrice.value = event.value.additionalDriverPrice!;
        totalAmount.value = event.value.additionalDriverPrice!;
        updateTotalAmount();
      } else if (type.value == "group") {
        eventPrice.value = event.value.price! * qty.value;
        totalAmount.value = event.value.price! * qty.value;
        updateTotalAmount();
      }
    }
  }

  void fillCouponCode(String couponCode) {
    couponCodeController.text = couponCode;
    applyCouponCode();
  }
}

class CreatePaymentIntentResult {
  String? clientSecret;
  String? ephemeralKey;
  String? customerId;

  CreatePaymentIntentResult(
      {this.clientSecret, this.ephemeralKey, this.customerId});
}
