import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import
import 'package:webview_flutter/webview_flutter.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../shared/support_button.dart';
import '../controllers/make_payment_controller.dart';

class MakePaymentView extends GetView<MakePaymentController> {
  const MakePaymentView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Payment', style: AppTextStyles.titleText),
        elevation: 1,
        automaticallyImplyLeading: true,
        actions: [
          IconButton(
              onPressed: () {
                controller.switchCurrency();
              },
              icon: Image.asset(
                'assets/images/sgdmyr.png',
                width: 30,
              ))
          // Padding(
          //   padding: const EdgeInsets.only(right: 10),
          //   child: Column(
          //     children: [
          //       Spacer(),
          //       Text(
          //           "Singapore Dollar",
          //           style: AppTextStyles.normalTextBold.copyWith(
          //               color: AppColor.primaryButtonColor,
          //               fontSize: 16
          //           )
          //       ),
          //       Spacer()
          //     ],
          //   ),
          // )
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: SingleChildScrollView(
        child: Obx(() {
          return Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
            child: (controller.isPaymentSuccess.value)
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 30),
                      const Icon(
                        Icons.check_circle_outline,
                        color: Colors.green,
                        size: 64,
                      ),
                      const SizedBox(height: 24),
                      (controller.totalAmount.value > 0)
                          ? const Text(
                              'Thank you for your payment!',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                              textAlign: TextAlign.center,
                            )
                          : const Text(
                              'Thank you!',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                              textAlign: TextAlign.center,
                            ),
                      const SizedBox(height: 16),
                      const Text(
                        'Your registration is now complete',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            controller.updateBookingStatus();
                            GoRouter.of(context)
                                .push(AppRoutePaths.eventsForm, extra: {
                              // Changed to GoRouter
                              "event": controller.event.value,
                              "type": controller.type.value
                            });
                          },
                          style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            padding: WidgetStateProperty.all<EdgeInsets>(
                                const EdgeInsets.all(20)),
                          ),
                          child: const Text(
                            'Continue',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (controller.hasWalletBalance) ...[
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.account_balance_wallet,
                                  color: Colors.green),
                              const SizedBox(width: 8),
                              Text(
                                'Available Credit: \$${controller.walletBalance}',
                                style: AppTextStyles.normalTextBold.copyWith(
                                  color: Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      const SizedBox(
                        height: 20,
                      ),
                      const Text(
                        'Your registration will only be finalized upon receipt of payment.',
                        style: AppTextStyles.normalText,
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(
                        height: 30,
                      ),

                      SizedBox(
                        width: double.infinity,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    controller.registrationItemTitle.value,
                                    style: AppTextStyles.normalText.copyWith(
                                      color: AppColor.primaryButtonColor,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    (controller.isCouponApplied.value)
                                        ? Text(
                                            '${controller.currencyPrefix} ${StringUtil.formatMoneyWithoutCent(controller.eventPrice.value)}',
                                            style: AppTextStyles.normalTextBold
                                                .copyWith(
                                              color: AppColor.redButtonColor,
                                              decoration:
                                                  TextDecoration.lineThrough,
                                            ),
                                          )
                                        : const SizedBox(),
                                    Text(
                                      '${controller.currencyPrefix} ${StringUtil.formatMoneyWithoutCent(controller.discountedPrice.value)}',
                                      style:
                                          AppTextStyles.normalTextBold.copyWith(
                                        color: AppColor.primaryButtonColor,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            if (controller.type.value == "group") ...[
                              const SizedBox(
                                height: 20,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      "Number of participants",
                                      style: AppTextStyles.normalText.copyWith(
                                        color: AppColor.primaryButtonColor,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        "${controller.qty.value}",
                                        style: AppTextStyles.normalTextBold
                                            .copyWith(
                                          color: AppColor.primaryButtonColor,
                                          fontSize: 18,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                            const SizedBox(
                              height: 20,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Total amount',
                                  style: AppTextStyles.normalTextBold.copyWith(
                                    color: AppColor.primaryButtonColor,
                                    fontSize: 18,
                                  ),
                                ),
                                Text(
                                  '${controller.currencyPrefix} ${StringUtil.formatMoneyWithoutCent(controller.totalAmount.value)}',
                                  style: AppTextStyles.normalTextBold.copyWith(
                                    color: AppColor.primaryButtonColor,
                                    fontSize: 25,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),

                      // hide coupon code if total amount is 0
                      if (controller.totalAmount.value > 0) ...[
                        SizedBox(
                          height: 50,
                          child: TextFormField(
                            readOnly: true,
                            controller: controller.couponCodeController,
                            autocorrect: false,
                            style: AppTextStyles.normalTextBold.copyWith(
                              fontSize: 20,
                            ),
                            textAlign: TextAlign.center,
                            decoration: const InputDecoration(
                                labelText: "Coupon Code", hintText: ""),
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                  onPressed: () async {
                                    //await controller.availableCouponCode();
                                    GoRouter.of(context).push(AppRoutePaths
                                        .selectCouponCode); // Changed to GoRouter
                                  },
                                  style: ButtonStyle(
                                      foregroundColor: WidgetStateProperty.all<Color>(
                                          Colors.white),
                                      backgroundColor:
                                          WidgetStateProperty.all<Color>(
                                              AppColor.primaryButtonColor),
                                      shape: WidgetStateProperty.all<
                                              RoundedRectangleBorder>(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(18.0),
                                              side: const BorderSide(
                                                  color: AppColor
                                                      .primaryButtonColor)))),
                                  child: const SizedBox(
                                    // padding: const EdgeInsets.only(left: 5, right: 5),
                                    height: 50,
                                    child: Center(
                                      child: Text("Select Coupon",
                                          style: TextStyle(fontSize: 16)),
                                    ),
                                  )),
                            ),
                            // const SizedBox(width: 20,),
                            // Expanded(
                            //   child: ElevatedButton(
                            //       onPressed: () async {
                            //         await controller.applyCouponCode();
                            //       },
                            //       style: ButtonStyle(
                            //           foregroundColor: MaterialStateProperty.all<Color>(
                            //               Colors.white),
                            //           backgroundColor: MaterialStateProperty.all<Color>(
                            //               AppColor.primaryButtonColor),
                            //           shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            //               RoundedRectangleBorder(
                            //                   borderRadius: BorderRadius.circular(18.0),
                            //                   side: const BorderSide(color: AppColor
                            //                       .primaryButtonColor)
                            //               )
                            //           )
                            //       ),
                            //       child: Container(
                            //         // padding: const EdgeInsets.only(left: 20, right: 20),
                            //         height: 50,
                            //         child: const Center(
                            //           child: Text(
                            //               "Apply Coupon",
                            //               style: TextStyle(fontSize: 16)
                            //           ),
                            //         ),
                            //       )
                            //   ),
                            // ),
                          ],
                        ),
                      ],

                      const SizedBox(
                        height: 50,
                      ),

                      // PAY WITH CARD

                      (controller.totalAmount.value > 0)
                          ? Column(
                              children: [
                                ElevatedButton(
                                    onPressed: () async {
                                      await controller.makePayment();
                                    },
                                    style: ButtonStyle(
                                        foregroundColor:
                                            WidgetStateProperty.all<Color>(
                                                Colors.white),
                                        backgroundColor:
                                            WidgetStateProperty.all<Color>(
                                                AppColor.primaryButtonColor),
                                        shape: WidgetStateProperty.all<
                                                RoundedRectangleBorder>(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(18.0),
                                                side: const BorderSide(
                                                    color: AppColor.primaryButtonColor)))),
                                    child: Container(
                                      padding: const EdgeInsets.only(
                                          left: 20, right: 20),
                                      height: 50,
                                      child: const Center(
                                        child: Text("Pay with Card",
                                            style: TextStyle(fontSize: 16)),
                                      ),
                                    )),
                                const SizedBox(
                                  height: 10,
                                ),

                                // PAY NOW

                                (controller.currency.value == "SGD")
                                    ? ElevatedButton(
                                        onPressed: () async {
                                          await controller.makePaymentPayNow();

                                          // Check if the widget is still mounted before using the context
                                          if (controller.isMounted) {
                                            showDialog(
                                              context: context,
                                              barrierDismissible: true,
                                              builder: (BuildContext context) {
                                                return SafeArea(
                                                  child: (controller
                                                              .payNowId.value ==
                                                          0)
                                                      ? Container()
                                                      : Stack(children: [
                                                          WebViewWidget(
                                                              controller: controller
                                                                  .webViewController),
                                                          Align(
                                                            alignment: Alignment
                                                                .topRight,
                                                            child: Container(
                                                              margin:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      top: 50.0,
                                                                      right:
                                                                          20.0),
                                                              child: IconButton(
                                                                onPressed: () {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                                icon: const Icon(
                                                                    Icons
                                                                        .close),
                                                                iconSize: 30,
                                                                color: Colors
                                                                    .black,
                                                              ),
                                                            ),
                                                          ),
                                                          Align(
                                                            alignment: Alignment
                                                                .topRight,
                                                            child: Container(
                                                              margin:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      top: 12,
                                                                      right:
                                                                          12),
                                                              child: Container(
                                                                width: 25,
                                                                height: 25,
                                                                decoration:
                                                                    const BoxDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ]),
                                                );
                                              },
                                            );
                                          }
                                        },
                                        style: ButtonStyle(
                                            foregroundColor: WidgetStateProperty
                                                .all<Color>(Colors.white),
                                            backgroundColor: WidgetStateProperty.all<Color>(
                                                AppColor.primaryButtonColor),
                                            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                                RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            18.0),
                                                    side: const BorderSide(
                                                        color: AppColor
                                                            .primaryButtonColor)))),
                                        child: Container(
                                          padding: const EdgeInsets.only(
                                              left: 20, right: 20),
                                          height: 50,
                                          child: const Center(
                                            child: Text("Pay with PayNow",
                                                style: TextStyle(fontSize: 16)),
                                          ),
                                        ))
                                    : Container(),

                                const SizedBox(height: 10),

                                // USE CREDIT

                                Obx(() => controller.isLoadingBalance
                                    ? const Center(
                                        child: SizedBox(
                                          height: 50,
                                          child: Center(
                                            child: CircularProgressIndicator(),
                                          ),
                                        ),
                                      )
                                    : controller.hasWalletBalance
                                        ? Column(
                                            children: [
                                              ElevatedButton(
                                                  onPressed: () => controller
                                                      .payWithCredit(),
                                                  style: ButtonStyle(
                                                      foregroundColor:
                                                          WidgetStateProperty.all<Color>(
                                                              Colors.white),
                                                      backgroundColor:
                                                          WidgetStateProperty.all<Color>(
                                                              AppColor
                                                                  .primaryButtonColor),
                                                      shape: WidgetStateProperty.all<
                                                              RoundedRectangleBorder>(
                                                          RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(18.0),
                                                              side: const BorderSide(color: AppColor.primaryButtonColor)))),
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 20,
                                                            right: 20),
                                                    height: 50,
                                                    child: const Center(
                                                      child: Text("Use Credit",
                                                          style: TextStyle(
                                                              fontSize: 16)),
                                                    ),
                                                  )),
                                              const SizedBox(height: 20),
                                            ],
                                          )
                                        : Container()),
                              ],
                            )
                          : ElevatedButton(
                              onPressed: () async {
                                if (controller.type.value ==
                                    "additional driver") {
                                  await controller
                                      .freeBookingForAdditionalDriver();
                                } else {
                                  await controller.freeBooking();
                                }
                              },
                              style: ButtonStyle(
                                  foregroundColor:
                                      WidgetStateProperty.all<Color>(
                                          Colors.white),
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColor.primaryButtonColor),
                                  shape: WidgetStateProperty.all<
                                          RoundedRectangleBorder>(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(18.0),
                                          side: const BorderSide(
                                              color: AppColor
                                                  .primaryButtonColor)))),
                              child: Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                height: 50,
                                child: const Center(
                                  child: Text("Free Registration",
                                      style: TextStyle(fontSize: 16)),
                                ),
                              )),
                    ],
                  ),
          );
        }),
      ),
    );
  }
}
