import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../routes/app_router.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../shared/support_button.dart';
import '../controllers/product_detail_controller.dart';
import '../../cart/controllers/cart_controller.dart';

class ProductDetailView extends StatefulWidget {
  const ProductDetailView({super.key});

  @override
  State<ProductDetailView> createState() => _ProductDetailViewState();
}

class _ProductDetailViewState extends State<ProductDetailView> {
  final ProductDetailController controller =
      Get.find<ProductDetailController>();
  final cartController = Get.find<CartController>();
  final GlobalKey _cartIconKey = GlobalKey();
  final GlobalKey _addToCartButtonKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _overlayEntry?.remove();
    super.dispose();
  }

  void _showAddToCartAnimation() {
    final RenderBox? buttonBox =
        _addToCartButtonKey.currentContext?.findRenderObject() as RenderBox?;
    final RenderBox? cartIconBox =
        _cartIconKey.currentContext?.findRenderObject() as RenderBox?;

    if (buttonBox == null || cartIconBox == null) return;

    // Calculate center positions
    final buttonCenter = buttonBox.localToGlobal(Offset(
      buttonBox.size.width / 2,
      buttonBox.size.height / 2,
    ));
    final cartIconCenter = cartIconBox.localToGlobal(Offset(
      cartIconBox.size.width / 2,
      cartIconBox.size.height / 2,
    ));

    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            TweenAnimationBuilder<Offset>(
              duration: const Duration(milliseconds: 600), // Faster animation
              curve: Curves.easeIn, // Accelerating curve
              tween: Tween<Offset>(
                begin: buttonCenter,
                end: cartIconCenter,
              ),
              builder: (context, Offset position, child) {
                return Positioned(
                  left: position.dx - 25, // Center the 50x50 widget
                  top: position.dy - 25,
                  child: TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 600),
                    curve: Curves.easeIn,
                    tween: Tween<double>(begin: 1.0, end: 0.5),
                    builder: (context, double scale, child) {
                      return Transform.scale(
                        scale: scale,
                        child: child,
                      );
                    },
                    child: child,
                  ),
                );
              },
              onEnd: () {
                _overlayEntry?.remove();
                _overlayEntry = null;
                controller
                    .addToCart(); // This will now call both API and local cart update
              },
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppColor.primaryButtonColor.withAlpha(26 * 9),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26 * 2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.shopping_cart,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ],
        );
      },
    );

    // Add a subtle button press effect
    buttonBox.parent?.markNeedsPaint();

    Overlay.of(context).insert(_overlayEntry!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Product Detail', style: AppTextStyles.titleText),
          elevation: 1,
          actions: [
            Stack(
              children: [
                IconButton(
                  key: _cartIconKey,
                  icon: const Icon(Icons.shopping_cart),
                  onPressed: () => context.push(AppRoutePaths.cart),
                ),
                Positioned(
                  right: 8,
                  top: 8,
                  child: Obx(() {
                    final count = cartController.cartCount.value;
                    return count > 0
                        ? Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: AppColor.redButtonColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              count.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          )
                        : const SizedBox();
                  }),
                ),
              ],
            ),
          ],
        ),
        floatingActionButton: const SupportButton(),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                key: _addToCartButtonKey,
                onPressed:
                    _showAddToCartAnimation, // Animation will trigger addToCart on completion
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(
                        AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.primaryButtonColor)))),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  child: const Text(
                    'Add to Cart',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
        ),
        body: Obx(() {
          return SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              (controller.product.value.images == null ||
                      controller.product.value.images!.isEmpty)
                  ? const SizedBox()
                  : CarouselSlider(
                      options: CarouselOptions(
                        height: 300.0,
                      ),
                      items: controller.product.value.images!.map((i) {
                        return Builder(
                          builder: (BuildContext context) {
                            return Container(
                                width: MediaQuery.of(context).size.width,
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 5.0),
                                child: Image.network(i, fit: BoxFit.cover));
                          },
                        );
                      }).toList(),
                    ),
              const SizedBox(height: 40),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  controller.product.value.name ?? "",
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.normalTextBold.copyWith(
                    fontSize: 23,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              if (controller.product.value.priceSale != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'SGD ${controller.product.value.priceSale}',
                        style: AppTextStyles.normalTextBold.copyWith(
                          fontSize: 16,
                          color: AppColor.redButtonColor,
                        ),
                      ),
                      const TextSpan(text: '  '),
                      TextSpan(
                        text: "SGD ${controller.product.value.price}",
                        style: AppTextStyles.normalText.copyWith(
                          decoration: TextDecoration.lineThrough,
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ]),
                  ),
                )
              else
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Text(
                    'SGD ${controller.product.value.price}',
                    style: AppTextStyles.normalText.copyWith(
                      fontSize: 16,
                    ),
                  ),
                ),
              const SizedBox(height: 30),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  '${controller.product.value.description}',
                  style: AppTextStyles.normalText.copyWith(
                    fontSize: 16,
                  ),
                ),
              ),
              const SizedBox(height: 30),
            ]),
          );
        }));
  }
}
