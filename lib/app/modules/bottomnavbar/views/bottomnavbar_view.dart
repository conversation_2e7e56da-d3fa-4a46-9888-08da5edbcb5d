import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../shared/kdrawer/views/kdrawer_view.dart';
import '../../shared/support_button.dart';
import '../controllers/bottomnavbar_controller.dart';

/// Modern bottom navigation bar view with best practices
class BottomNavBarView extends GetView<BottomNavBarController> {
  const BottomNavBarView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      drawer: SizedBox(width: Get.width, child: KDrawerView()),
      body: Obx(() => _buildBody()),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: const SupportButton(),
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    if (controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColor.primaryColor),
        ),
      );
    }

    if (!controller.isInitialized) {
      return const Center(
        child: Text(
          'Initializing...',
          style: TextStyle(
            fontSize: 16,
            color: AppColor.primaryColor,
          ),
        ),
      );
    }

    return controller.getCurrentPageWidget();
  }

  /// Build the bottom navigation bar
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(BottomNavBarConstants.borderRadius),
          topRight: Radius.circular(BottomNavBarConstants.borderRadius),
        ),
        boxShadow: BottomNavBarConstants.boxShadow,
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(BottomNavBarConstants.borderRadius),
          topRight: Radius.circular(BottomNavBarConstants.borderRadius),
        ),
        child: Obx(() => BottomNavigationBar(
              elevation: BottomNavBarConstants.elevation,
              backgroundColor: Colors.white,
              type: BottomNavigationBarType.fixed,
              onTap: controller.changePage,
              selectedLabelStyle: const TextStyle(
                color: AppColor.secondaryColor,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: const TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
              ),
              unselectedItemColor: AppColor.primaryColor,
              selectedItemColor: AppColor.secondaryColor,
              currentIndex: controller.currentIndex,
              showSelectedLabels: false,
              showUnselectedLabels: false,
              iconSize: BottomNavBarConstants.iconSize,
              items: _buildNavigationItems(),
            )),
      ),
    );
  }

  /// Build navigation items
  List<BottomNavigationBarItem> _buildNavigationItems() {
    return controller.navigationPages
        .map((page) => _buildNavigationItem(page))
        .toList();
  }

  /// Build a single navigation item
  BottomNavigationBarItem _buildNavigationItem(NavigationPage page) {
    return BottomNavigationBarItem(
      icon: Obx(() => _buildNavigationIcon(page)),
      label: page.title,
    );
  }

  /// Build navigation icon with state management
  Widget _buildNavigationIcon(NavigationPage page) {
    final isSelected = controller.isPageActive(page.index);

    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BottomNavBarConstants.verticalPadding,
        horizontal: BottomNavBarConstants.horizontalPadding,
      ),
      child: Image.asset(
        isSelected ? page.iconOnPath : page.iconOffPath,
        height: BottomNavBarConstants.iconSize,
        errorBuilder: (context, error, stackTrace) {
          // Fallback icon if image fails to load
          return Icon(
            _getFallbackIcon(page.index),
            size: BottomNavBarConstants.iconSize,
            color: isSelected ? AppColor.secondaryColor : AppColor.primaryColor,
          );
        },
      ),
    );
  }

  /// Get fallback icon for each page
  IconData _getFallbackIcon(int index) {
    switch (index) {
      case 0:
        return Icons.home;
      case 1:
        return Icons.article;
      case 2:
        return Icons.event;
      case 3:
        return Icons.leaderboard;
      case 4:
        return Icons.card_giftcard;
      case 5:
        return Icons.store;
      case 6:
        return Icons.person;
      default:
        return Icons.circle;
    }
  }
}

/// Constants for bottom navigation bar styling
class BottomNavBarConstants {
  static const double iconSize = 30.0;
  static const double verticalPadding = 0.0;
  static const double horizontalPadding = 5.0;
  static const double borderRadius = 25.0;
  static const double elevation = 10.0;

  static const List<BoxShadow> boxShadow = [
    BoxShadow(
      offset: Offset(0.0, 1.0),
      blurRadius: 4.0,
      color: Colors.black12,
      spreadRadius: 2.0,
    ),
  ];
}
