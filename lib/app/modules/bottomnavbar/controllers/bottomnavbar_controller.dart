import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../helpers/debug_helper.dart';
import '../../../helpers/notification_util.dart';
import '../../../routes/app_router.dart';
import '../../account/views/account_view.dart';
import '../../events/views/events_view.dart';
import '../../home/<USER>/home_view.dart';
import '../../leaderboard/views/leaderboard_view.dart';
import '../../news/views/news_view.dart';
import '../../reward/views/reward_view.dart';
import '../../store/views/store_view.dart';

/// Bottom navigation bar controller with modern architecture and best practices
class BottomNavBarController extends GetxController with WidgetsBindingObserver {
  // Private reactive variables
  final _currentIndex = 0.obs;
  final _isInitialized = false.obs;
  final _isLoading = false.obs;

  // Public getters
  int get currentIndex => _currentIndex.value;
  bool get isInitialized => _isInitialized.value;
  bool get isLoading => _isLoading.value;

  // Safe UserController access
  UserController? _userController;
  UserController get userController {
    _userController ??= Get.isRegistered<UserController>()
        ? Get.find<UserController>()
        : Get.put(UserController(), permanent: true);
    return _userController!;
  }

  // Navigation pages configuration
  static const List<NavigationPage> _navigationPages = [
    NavigationPage(
      index: 0,
      title: 'Home',
      iconPath: 'assets/images/ic_home',
    ),
    NavigationPage(
      index: 1,
      title: 'News',
      iconPath: 'assets/images/ic_news',
    ),
    NavigationPage(
      index: 2,
      title: 'Events',
      iconPath: 'assets/images/ic_event',
    ),
    NavigationPage(
      index: 3,
      title: 'Leaderboard',
      iconPath: 'assets/images/ic_results',
    ),
    NavigationPage(
      index: 4,
      title: 'Reward',
      iconPath: 'assets/images/ic_reward',
    ),
    NavigationPage(
      index: 5,
      title: 'Store',
      iconPath: 'assets/images/ic_store',
    ),
    NavigationPage(
      index: 6,
      title: 'Profile',
      iconPath: 'assets/images/ic_profile',
    ),
  ];

  // Lazy-loaded page widgets
  List<Widget> get pageWidgets => [
        HomeView(),
        NewsView(),
        EventsView(),
        LeaderboardView(),
        RewardView(),
        StoreView(),
        AccountView(),
      ];

  // Navigation pages getter
  List<NavigationPage> get navigationPages => _navigationPages;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeController();
  }

  @override
  void onReady() {
    super.onReady();
    DebugHelper.d('BottomNavBarController ready');
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  /// Initialize the controller with proper error handling
  Future<void> _initializeController() async {
    try {
      _isLoading.value = true;

      // Initialize notifications
      await _initializeNotifications();

      // Handle navigation arguments
      _handleNavigationArguments();

      // Setup user authentication
      await _setupUserAuthentication();

      // Add lifecycle observer
      WidgetsBinding.instance.addObserver(this);

      _isInitialized.value = true;
      DebugHelper.d('BottomNavBarController initialized successfully');
    } catch (e, stackTrace) {
      DebugHelper.d('Error initializing BottomNavBarController: $e');
      DebugHelper.d('StackTrace: $stackTrace');
      // Set to initialized anyway to prevent blocking the UI
      _isInitialized.value = true;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Initialize notification services
  Future<void> _initializeNotifications() async {
    try {
      await NotificationUtil().registerTokenAndSubscribe();
      NotificationUtil().checkPushNotificationInitialMessage();
      DebugHelper.d('Notifications initialized successfully');
    } catch (e) {
      DebugHelper.d('Error initializing notifications: $e');
    }
  }

  /// Handle navigation arguments safely
  void _handleNavigationArguments() {
    try {
      final arguments = Get.arguments;
      if (arguments is int && _isValidPageIndex(arguments)) {
        _currentIndex.value = arguments;
        DebugHelper.d('Set initial page index to: $arguments');
      }
    } catch (e) {
      DebugHelper.d('Error handling navigation arguments: $e');
    }
  }

  /// Setup user authentication and related services
  Future<void> _setupUserAuthentication() async {
    try {
      if (userController.isLoggedIn.value) {
        await _handleLoggedInUser();
      } else {
        _handleLoggedOutUser();
      }
    } catch (e) {
      DebugHelper.d('Error setting up user authentication: $e');
    }
  }

  /// Handle services for logged-in users
  Future<void> _handleLoggedInUser() async {
    try {
      await Future.wait([
        userController.callRegisterFcmTokenApi().catchError((e) {
          DebugHelper.d('Error registering FCM token: $e');
          return Future.value();
        }),
        userController.callGetUnreadNotificationCountApi().catchError((e) {
          DebugHelper.d('Error getting notification count: $e');
          return Future.value();
        }),
      ]);
      DebugHelper.d('User services initialized successfully');
    } catch (e) {
      DebugHelper.d('Error handling logged-in user: $e');
    }
  }

  /// Handle logged-out user state
  void _handleLoggedOutUser() {
    DebugHelper.d('User not logged in, redirecting to guest page');
    AppRouter.router.go(AppRoutePaths.guest);
  }

  /// Change the current page index
  void changePage(int newIndex) {
    if (!_isValidPageIndex(newIndex)) {
      DebugHelper.d('Invalid page index: $newIndex');
      return;
    }

    if (newIndex != _currentIndex.value) {
      DebugHelper.d('Changing page from ${_currentIndex.value} to $newIndex');
      _currentIndex.value = newIndex;
    }
  }

  /// Get the current page widget
  Widget getCurrentPageWidget() {
    try {
      if (_isValidPageIndex(_currentIndex.value)) {
        return pageWidgets[_currentIndex.value];
      } else {
        DebugHelper.d('Invalid current index, returning home page');
        _currentIndex.value = 0;
        return pageWidgets[0];
      }
    } catch (e) {
      DebugHelper.d('Error getting current page widget: $e');
      return HomeView(); // Safe fallback
    }
  }

  /// Navigate to chat list
  void navigateToChat() {
    try {
      AppRouter.router.push(AppRoutePaths.chatList);
    } catch (e) {
      DebugHelper.d('Error navigating to chat: $e');
    }
  }

  /// Check if page index is valid
  bool _isValidPageIndex(int index) {
    return index >= 0 && index < pageWidgets.length;
  }

  /// Check if a specific page is currently active
  bool isPageActive(int pageIndex) => _currentIndex.value == pageIndex;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    _handleAppLifecycleChange(state);
  }

  /// Handle app lifecycle changes
  void _handleAppLifecycleChange(AppLifecycleState state) {
    try {
      switch (state) {
        case AppLifecycleState.resumed:
          _handleAppResumed();
          break;
        case AppLifecycleState.paused:
          DebugHelper.d('App paused');
          break;
        case AppLifecycleState.inactive:
          DebugHelper.d('App inactive');
          break;
        case AppLifecycleState.detached:
          DebugHelper.d('App detached');
          break;
        case AppLifecycleState.hidden:
          DebugHelper.d('App hidden');
          break;
      }
    } catch (e) {
      DebugHelper.d('Error handling app lifecycle change: $e');
    }
  }

  /// Handle app resume
  Future<void> _handleAppResumed() async {
    try {
      DebugHelper.d('App resumed');
      if (userController.isLoggedIn.value) {
        // Refresh notification count after a short delay
        await Future.delayed(const Duration(milliseconds: 500));
        await userController.callGetUnreadNotificationCountApi().catchError((e) {
          DebugHelper.d('Error refreshing notification count: $e');
        });
      }
    } catch (e) {
      DebugHelper.d('Error handling app resumed: $e');
    }
  }
}

/// Navigation page configuration model
class NavigationPage {
  const NavigationPage({
    required this.index,
    required this.title,
    required this.iconPath,
  });

  final int index;
  final String title;
  final String iconPath;

  String get iconOnPath => '${iconPath}_on.png';
  String get iconOffPath => '${iconPath}_off.png';
}
