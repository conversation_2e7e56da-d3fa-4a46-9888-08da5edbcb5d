import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:http/http.dart' as http;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../constants/app_config.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/vehicle_model.dart';
import '../../../services/api_client.dart';
import '../../vehicles/controllers/vehicles_controller.dart';

class VehiclesEditController extends BaseScreenController {
  @override
  String get screenName => 'Vehicles Edit';

  TextEditingController makeController = TextEditingController();
  TextEditingController modelController = TextEditingController();
  TextEditingController yearController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  var msg = "";

  var vehicle = Vehicle().obs;
  var selectedType = "".obs;
  var image64 = "".obs;
  int vehicleId = 0;

  final UserController userController = Get.put(UserController());
  final VehiclesController vehiclesController = Get.put(VehiclesController());

  final ImagePicker _picker = ImagePicker();

  bool isImageUpdated = false;

  var typeItems = ['SG Registered', 'MY Registered', 'Track Car'];

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;
    vehicle.value = data;

    if (vehicle.value.image != null) {
      networkImageToBase64("${AppConfig.storageUrl}${vehicle.value.image!}")
          .then((value) {
        image64.value = value!;
      });
    }

    vehicleId = vehicle.value.id!;

    makeController.text = vehicle.value.make!;
    modelController.text = vehicle.value.model!;
    yearController.text = vehicle.value.year ?? "";
    selectedType.value = vehicle.value.type!;
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<String?> networkImageToBase64(String imageUrl) async {
    http.Response response = await http.get(Uri.parse(imageUrl));
    final bytes = response.bodyBytes;
    return base64Encode(bytes);
  }

  Future<bool> editVehicle() async {
    try {
      EasyLoading.show(status: 'Please wait...');

      if (isImageUpdated) {
        var response = await ApiClient.editVehicles(
            userController.getToken(),
            userController.user.value.id!,
            vehicleId,
            makeController.text,
            modelController.text,
            yearController.text,
            selectedType.value,
            image64.value);

        var success = (jsonDecode(response)['success'])
            ? jsonDecode(response)['success']
            : false;
        msg = jsonDecode(response)['message'];
        debugPrint(msg);

        EasyLoading.dismiss();

        if (success) {
          vehiclesController.getVehicles();
        }

        return success;
      } else {
        EasyLoading.show(status: 'Please wait...');

        var response = await ApiClient.editVehicles(
            userController.getToken(),
            userController.user.value.id!,
            vehicleId,
            makeController.text,
            modelController.text,
            yearController.text,
            selectedType.value,
            null);

        var success = (jsonDecode(response)['success'] == null)
            ? false
            : jsonDecode(response)['success'];
        msg = jsonDecode(response)['message'];

        EasyLoading.dismiss();

        if (success) {
          vehiclesController.getVehicles();
        }

        return success;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> getImageFromGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    final imageBytes = await image?.readAsBytes();
    image64.value = base64.encode(imageBytes!);
    isImageUpdated = true;
    Navigator.of(Get.context!).pop();
  }

  Future<void> getImageFromCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    final imageBytes = await image?.readAsBytes();
    image64.value = base64.encode(imageBytes!);
    isImageUpdated = true;
    Navigator.of(Get.context!).pop();
  }

  Future<bool> deleteVehicle() async {
    try {
      EasyLoading.show(status: 'Please wait...');

      var response = await ApiClient.deleteVehicles(
          userController.getToken(), userController.user.value.id!, vehicleId);

      var success = (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      EasyLoading.dismiss();

      if (success) {
        vehiclesController.getVehicles();
      }

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
