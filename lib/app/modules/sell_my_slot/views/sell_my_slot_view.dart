import 'package:automoment/app/modules/shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import 'package:flutter/material.dart';

// import 'package:get/get.dart'; // Removed Get import
import 'package:go_router/go_router.dart'; // Added GoRouter import

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
// import '../../../routes/app_pages.dart'; // Removed GetX routes
import '../../../routes/app_router.dart'; // Added AppRouter for AppRoutePaths
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/sell_my_slot_controller.dart';

class SellMySlotView extends StatelessWidget {
  final SellMySlotController controller;

  // Updated to use GoRouter for controller (if needed, or ensure controller is provided via other means)
  // For simplicity, assuming controller is already available or passed via GoRouter's state management if applicable.
  // If controller was obtained via Get.find, this part needs careful refactoring based on DI strategy.
  // SellMySlotView({super.key}) : controller = Get.find<SellMySlotController>();
  // This example assumes controller is passed or obtained differently:
  SellMySlotView({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Sell My Slot', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: SingleChildScrollView(
            child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                controller.event.value.title!,
                style: AppTextStyles.titleText,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'Event Date: ${controller.showEventDate()}',
                style: AppTextStyles.normalText,
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'To whom are you transferring your slot? Please make sure they are a registered user of Automoment app.',
                style: AppTextStyles.normalText,
              ),
            ),
            emailLookUpWidget(context),
          ]),
        )));
  }

  emailLookUpWidget(BuildContext context) {
    // show an input box to enter email and check button on the right side to lookup the user details

    return ValueListenableBuilder(
        valueListenable: ValueNotifier<bool>(controller.isUserExist.value),
        builder: (context, value, child) {
          return Container(
              margin: const EdgeInsets.symmetric(vertical: 20),
              width: double.infinity,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Form(
                      key: controller.formKey,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width -
                                150, // Replaced Get.width
                            child: TextFormField(
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return 'Required';
                                }
                                return null;
                              },
                              style: AppTextStyles.normalText.copyWith(
                                fontSize: 14,
                              ),
                              controller: controller.emailController,
                              keyboardType: TextInputType.emailAddress,
                              decoration: const InputDecoration(
                                //border: OutlineInputBorder(),
                                hintText: 'Enter email address',
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          ElevatedButton(
                              style: ButtonStyle(
                                  foregroundColor: WidgetStateProperty.all<Color>(
                                      Colors.white),
                                  backgroundColor: WidgetStateProperty.all<Color>(
                                      AppColor.primaryButtonColor),
                                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(18.0),
                                          side: const BorderSide(
                                              color: AppColor
                                                  .primaryButtonColor)))),
                              onPressed: () async {
                                if (controller.formKey.currentState!
                                    .validate()) {
                                  // check if this is his own email
                                  if (controller
                                          .userController.user.value.email ==
                                      controller.emailController.text) {
                                    showDialog(
                                        context: context,
                                        builder: (dialogContext) =>
                                            KMessageDialogView(
                                                content:
                                                    "You cannot transfer your slot to yourself.",
                                                callback: () {
                                                  Navigator.of(dialogContext)
                                                      .pop(); // Replaced Get.back()
                                                }));
                                  } else {
                                    if (await controller.checkUserExist()) {
                                    } else {
                                      showDialog(
                                          context: context,
                                          builder: (dialogContext) =>
                                              KMessageDialogView(
                                                  content:
                                                      "The email is not registered. Please check again.",
                                                  callback: () {
                                                    Navigator.of(dialogContext)
                                                        .pop(); // Replaced Get.back()
                                                  }));
                                    }
                                  }
                                }
                              },
                              child: Text('Check',
                                  style: AppTextStyles.normalText
                                      .copyWith(fontSize: 14, color: Colors.white)))
                        ],
                      ),
                    ),

                    // show user info
                    controller.isUserExist.value
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                                const SizedBox(height: 30),
                                Text(
                                    "Your slot will be transferred to the following user.",
                                    style: AppTextStyles.normalText.copyWith(
                                      fontSize: 14,
                                    )),
                                const SizedBox(height: 20),
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: 70,
                                        child: Text("Name",
                                            style: AppTextStyles.normalText
                                                .copyWith(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      SizedBox(
                                        width: 20,
                                        child: Text(" :",
                                            style: AppTextStyles.normalText
                                                .copyWith(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      Flexible(
                                        child: Text(controller.buyerName.value,
                                            style: AppTextStyles.normalText
                                                .copyWith(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                    ]),
                                const SizedBox(height: 10),
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: 70,
                                        child: Text("Email",
                                            style: AppTextStyles.normalText
                                                .copyWith(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      SizedBox(
                                        width: 20,
                                        child: Text(" :",
                                            style: AppTextStyles.normalText
                                                .copyWith(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      Flexible(
                                        child: Text(controller.buyerEmail.value,
                                            style: AppTextStyles.normalText
                                                .copyWith(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                    ]),
                                const SizedBox(height: 30),
                                ElevatedButton(
                                    onPressed: () async {
                                      showDialog(
                                          context: context,
                                          builder: (dialogContext) =>
                                              KConfirmDialogView(
                                                content:
                                                    "Are you sure want to sell your slot?",
                                                callbackNo: () {
                                                  Navigator.of(dialogContext)
                                                      .pop(); // Replaced Get.back()
                                                },
                                                callbackYes: () async {
                                                  Navigator.of(dialogContext)
                                                      .pop(); // Dismiss confirm dialog first
                                                  if (await controller
                                                      .sellMySlot()) {
                                                    showDialog(
                                                        context:
                                                            context, // Use original context for new dialog
                                                        barrierDismissible:
                                                            false,
                                                        builder:
                                                            (messageDialogContext) =>
                                                                KMessageDialogView(
                                                                  content:
                                                                      controller
                                                                          .msg,
                                                                  callback: () {
                                                                    Navigator.of(
                                                                            messageDialogContext)
                                                                        .pop(); // Dismiss message dialog
                                                                    GoRouter.of(context).go(
                                                                        AppRoutePaths
                                                                            .bottomBar,
                                                                        extra:
                                                                            2); // Replaced Get.offAllNamed
                                                                  },
                                                                ));
                                                  } else {
                                                    showDialog(
                                                        context:
                                                            context, // Use original context for new dialog
                                                        barrierDismissible:
                                                            false,
                                                        builder:
                                                            (messageDialogContext) =>
                                                                KMessageDialogView(
                                                                  content:
                                                                      controller
                                                                          .msg,
                                                                  callback: () {
                                                                    Navigator.of(
                                                                            messageDialogContext)
                                                                        .pop(); // Replaced Get.back()
                                                                    // Navigator.of(context).pop(); // This second Get.back() might have been to pop the KConfirmDialogView, which is handled above.
                                                                  },
                                                                ));
                                                  }
                                                },
                                              ));
                                    },
                                    style: ButtonStyle(
                                        foregroundColor:
                                            WidgetStateProperty.all<Color>(
                                                Colors.white),
                                        backgroundColor:
                                            WidgetStateProperty.all<Color>(
                                                AppColor.redButtonColor),
                                        shape: WidgetStateProperty.all<
                                                RoundedRectangleBorder>(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(18.0),
                                                side: const BorderSide(
                                                    color: AppColor.redButtonColor)))),
                                    child: Container(
                                      padding: const EdgeInsets.only(
                                          left: 20, right: 20),
                                      height: 50,
                                      child: const Center(
                                        child: Text("Sell My Slot",
                                            style: TextStyle(fontSize: 16)),
                                      ),
                                    )),
                              ])
                        : const SizedBox(),
                  ]));
        });
  }
}
