import 'dart:convert';

import 'package:automoment/app/helpers/snack_helper.dart';
import '../../../routes/app_router.dart'; // Corrected import path
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../constants/app_config.dart';
import '../../../controllers/user_controller.dart';
import '../../cart/controllers/cart_controller.dart';
import '../../../services/api_client.dart';

class ProductPaymentController extends BaseScreenController {
  @override
  String get screenName => 'Product Payment';

  final UserController userController = Get.find<UserController>();

  final currency = "SGD".obs;
  final currencyPrefix = "SGD".obs;
  final orderId = 0.obs;
  final orderDetails = Rxn<Map<String, dynamic>>();
  final cartController = Get.find<CartController>();
  final isLoading = false.obs;
  final isProcessingPayment = false.obs;
  final totalAmount = 0.0.obs;
  final totalAmountMyr = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments['order_id'] != null) {
      orderId.value = Get.arguments['order_id'];
      loadOrderDetails();
    }

    // Initialize Stripe
    Stripe.publishableKey = AppConfig.stripePublishableKey;
    Stripe.instance.applySettings();
  }

  Future<void> loadOrderDetails() async {
    try {
      isLoading.value = true;
      final token = userController.getToken();
      if (token == null || token.toString().isEmpty) {
        throw Exception('User token not found');
      }

      final response =
          await ApiClient.getOrderDetail(token, orderId.value.toString());
      final data = json.decode(response);

      if (data['success'] == true && data['data'] != null) {
        var orderData = data['data'];
        // Use items from API response
        orderDetails.value = orderData;

        // Ensure items is initialized even if empty
        if (orderDetails.value!['items'] == null) {
          orderDetails.value!['items'] = [];
        }

        // Update amounts from the API response
        totalAmount.value =
            double.tryParse(orderData['total_amount_sgd'] ?? '0') ?? 0.0;
        totalAmountMyr.value =
            double.tryParse(orderData['total_amount_myr'] ?? '0') ?? 0.0;
      } else {
        throw Exception(data['message'] ?? 'Failed to load order details');
      }
    } catch (e) {
      SnackHelper.showError('Failed to load order details: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  void updateAmounts() {
    if (orderDetails.value == null) return;

    if (currency.value == 'SGD') {
      totalAmount.value =
          double.tryParse(orderDetails.value!['total_amount_sgd'] ?? '0') ??
              0.0;
    } else {
      totalAmount.value =
          double.tryParse(orderDetails.value!['total_amount_myr'] ?? '0') ??
              0.0;
    }

    // Update the items list to show correct prices
    if (orderDetails.value!['items'] != null) {
      orderDetails.refresh();
    }
  }

  void switchCurrency() {
    if (currency.value == 'SGD') {
      currency.value = 'MYR';
      currencyPrefix.value = 'RM';
    } else {
      currency.value = 'SGD';
      currencyPrefix.value = 'SGD';
    }
    updateAmounts();
  }

  Future<void> makeStripePayment() async {
    if (isProcessingPayment.value) return;
    try {
      EasyLoading.show(status: 'Please wait...');
      isProcessingPayment.value = true;
      final response = await ApiClient.stripePaymentCart(
        userController.getToken(),
        orderId.value,
        currency.value,
      );
      EasyLoading.dismiss();

      final jsonResponse = Map<String, dynamic>.from(json.decode(response));
      if (jsonResponse['success'] == true) {
        final clientSecret = jsonResponse['client_secret'];

        // Configure payment sheet with return URL
        await Stripe.instance.initPaymentSheet(
          paymentSheetParameters: SetupPaymentSheetParameters(
            merchantDisplayName: 'Automoment',
            paymentIntentClientSecret: clientSecret,
            style: ThemeMode.light,
            returnURL: 'automoment://payment', // Add return URL scheme
            billingDetails: const BillingDetails(
                email: null, // Will be collected by Stripe
                phone: null,
                address: null),
          ),
        );

        // Present the payment sheet
        await Stripe.instance.presentPaymentSheet();

        // Verify the payment status
        final paymentIntent =
            await Stripe.instance.retrievePaymentIntent(clientSecret);
        if (paymentIntent.status == PaymentIntentsStatus.Succeeded) {
          SnackHelper.showSuccess('Payment completed successfully');
          // Navigate with proper stack using GoRouter
          AppRouter.router.go(AppRoutePaths.bottomBar,
              extra: {'index': 6}); // Reset to bottom navigation

          AppRouter.router
              .push(AppRoutePaths.orderStatus); // Navigate to Orders
          AppRouter.router.push(
            AppRoutePaths.orderStatusDetail,
            extra: {'order_id': orderId.value},
          ); // Navigate to Order Detail
        } else {
          throw Exception(
              'Payment not completed. Status: ${paymentIntent.status}');
        }
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is StripeException) {
        SnackHelper.showError('Payment failed: ${e.error.localizedMessage}');
      } else {
        SnackHelper.showError('Payment failed: ${e.toString()}');
      }
    } finally {
      isProcessingPayment.value = false;
    }
  }

  Future<void> makePayNowPayment() async {
    try {
      isProcessingPayment.value = true;
      EasyLoading.show(status: 'Please wait...');
      SnackHelper.showInfo('PayNow payment is not yet implemented');
    } catch (e) {
      SnackHelper.showError('Payment failed: ${e.toString()}');
    } finally {
      EasyLoading.dismiss();
      isProcessingPayment.value = false;
    }
  }
}
