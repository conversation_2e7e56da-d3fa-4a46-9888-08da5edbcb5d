import 'package:automoment/app/models/news_model.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Add this import

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
// import '../../../routes/app_pages.dart'; // Remove this import
import '../../../routes/app_router.dart'; // Add this import
import '../controllers/home_controller.dart';

class HomeView extends StatelessWidget {
  HomeView({super.key});

  final HomeController controller = Get.put(HomeController());

  @override
  Widget build(BuildContext context) {
    // String name = controller.userController.user.value.name != null ?
    //   "Hi, ${controller.userController.user.value.name}" : "Automoment";
    String name = "Automoment";

    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          title: Text(
            name,
            style: const TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
          elevation: 1,
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: const Icon(Icons.menu),
            iconSize: 30,
            color: Colors.black,
          ),
          actions: [
            IconButton(
              padding: const EdgeInsets.only(right: 10),
              onPressed: () {
                GoRouter.of(context).push(AppRoutePaths.notification);
              },
              icon: Obx(() {
                return Stack(
                  children: [
                    const Icon(Icons.notifications,
                        color: Colors.black, size: 30),
                    (controller.userController.unreadNotificationCount.value >
                            0)
                        ? Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(1),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(6)),
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 12,
                                minHeight: 12,
                              ),
                              child: Text(
                                (controller.userController
                                            .unreadNotificationCount.value >
                                        99)
                                    ? "99+"
                                    : controller.userController
                                        .unreadNotificationCount.value
                                        .toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )
                        : const SizedBox()
                  ],
                );
              }),
            ),
          ],
        ),
        body: SingleChildScrollView(
            child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Obx(() {
            return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        "Personal Best",
                        style: AppTextStyles.normalText.copyWith(
                          fontSize: 15,
                        ),
                      ),
                      const SizedBox(width: 3),
                      Image.asset(
                        "assets/images/stopwatch.png",
                        height: 15,
                        width: 15,
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 5,
                  ),

                  Text(
                    controller.personalBest.value,
                    style: AppTextStyles.normalTextBold.copyWith(
                      fontSize: 20,
                    ),
                  ),

                  // upcoming events

                  (controller.upcomingEvents.isNotEmpty)
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                              const SizedBox(height: 20),
                              Text(
                                controller.upcomingEvents.length == 1
                                    ? "Upcoming Event"
                                    : "Upcoming Events",
                                style: AppTextStyles.normalText.copyWith(
                                  fontSize: 15,
                                ),
                              ),
                              const SizedBox(height: 5),
                              CarouselSlider.builder(
                                itemCount: controller.upcomingEvents.length,
                                itemBuilder: (context, index, realIndex) {
                                  return GestureDetector(
                                      onTap: () {
                                        GoRouter.of(context).push(
                                            AppRoutePaths.eventsDetail,
                                            extra:
                                                "${controller.upcomingEvents[index].id}");
                                      },
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(right: 5),
                                        child: ClipRRect(
                                          borderRadius: const BorderRadius.all(
                                            Radius.circular(10),
                                          ),
                                          child: Image.network(
                                              "${AppConfig.storageUrl}${controller.upcomingEvents[index].image}",
                                              width: 150,
                                              height: 150,
                                              fit: BoxFit.cover, errorBuilder:
                                                  (BuildContext context,
                                                      Object exception,
                                                      StackTrace? stackTrace) {
                                            return Image.asset(
                                              'assets/images/image_placeholder.png',
                                              width: 150,
                                              height: 150,
                                              fit: BoxFit.cover,
                                            );
                                          }),
                                        ),
                                      ));
                                },
                                options: CarouselOptions(
                                  height: 150.0,
                                  autoPlay: false,
                                  viewportFraction: 0.5,
                                  enableInfiniteScroll: false,
                                  disableCenter: true,
                                  padEnds: false,
                                ),
                              )
                            ])
                      : const SizedBox(),

                  // my vehicles

                  (controller.vehicles.isNotEmpty)
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                              const SizedBox(height: 20),
                              Text(
                                  controller.vehicles.length == 1
                                      ? "My Vehicle"
                                      : "My Vehicles",
                                  style: AppTextStyles.normalText.copyWith(
                                    fontSize: 15,
                                  )),
                              const SizedBox(height: 5),
                              CarouselSlider.builder(
                                itemCount: controller.vehicles.length,
                                itemBuilder: (context, index, realIndex) {
                                  return GestureDetector(
                                    onTap: () {
                                      GoRouter.of(context).push(
                                          AppRoutePaths.vehiclesEdit,
                                          extra: controller.vehicles[index]);
                                    },
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(right: 5.0),
                                      child: ClipRRect(
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(10),
                                        ),
                                        child: Stack(
                                          children: [
                                            Image.network(
                                                "${AppConfig.storageUrl}${controller.vehicles[index].image}",
                                                width: 350,
                                                height: 150,
                                                fit: BoxFit.cover, errorBuilder:
                                                    (BuildContext context,
                                                        Object exception,
                                                        StackTrace?
                                                            stackTrace) {
                                              return Image.asset(
                                                'assets/images/placeholder_vehicle.jpg',
                                                width: 350,
                                                height: 150,
                                                fit: BoxFit.cover,
                                              );
                                            }),
                                            Container(
                                              width: 350,
                                              height: 150,
                                              decoration: const BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.bottomCenter,
                                                  end: Alignment.topCenter,
                                                  colors: [
                                                    Colors.black,
                                                    Colors.transparent
                                                  ],
                                                ),
                                              ),
                                              child: Column(
                                                children: [
                                                  const Spacer(),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            bottom: 10),
                                                    child: Text(
                                                      "${controller.vehicles[index].make!} ${controller.vehicles[index].model!} ",
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 13,
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                                options: CarouselOptions(
                                  height: 150.0,
                                  autoPlay: false,
                                  viewportFraction: 0.5,
                                  enableInfiniteScroll: false,
                                  disableCenter: true,
                                  padEnds: false,
                                ),
                              )
                            ])
                      : const SizedBox(),

                  // latest news

                  const SizedBox(height: 20),
                  Text("Latest News",
                      style: AppTextStyles.normalText.copyWith(
                        fontSize: 15,
                      )),
                  const SizedBox(height: 5),
                  for (var i = 0; i < controller.news.length; i++)
                    newsItem(controller.news[i]),
                ]);
          }),
        )));
  }

  Widget newsItem(News news) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: GestureDetector(
        onTap: () {
          GoRouter.of(Get.context!)
              .push(AppRoutePaths.newsDetail, extra: "${news.id}");
        },
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(5)),
              child: Image.network(
                "${AppConfig.storageUrl}${news.image}",
                width: 60,
                height: 60,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
                child: Text(
              news.title ?? '',
              style: AppTextStyles.normalText.copyWith(
                fontSize: 15,
              ),
            )),
          ],
        ),
      ),
    );
  }
}
