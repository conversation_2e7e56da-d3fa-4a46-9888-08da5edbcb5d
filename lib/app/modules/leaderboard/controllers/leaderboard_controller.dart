import 'dart:convert';

import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../models/event_modal.dart';
import '../../../models/leaderboard_response.dart';
import '../../../models/result_position.dart';
import '../../../services/api_client.dart';

class LeaderboardController extends BaseScreenController {
  @override
  String get screenName => 'Leaderboard';

  var isLoading = false.obs;
  var eventList = <Event>[].obs;
  var resultPositionList = <ResultPosition>[].obs;
  var resultPositionAllList = <ResultPosition>[].obs;
  var showScoringPopup = false.obs;
  var currentScoringType = 'best_time'.obs;
  var tempScoringType = 'best_time'.obs;
  var sortBy = 'position'.obs;
  var selectedScoringTypes = <String>{}.obs;
  final RxBool showCarMakerFilter = false.obs;
  final RxList<String> selectedCarMakers = <String>["all"].obs;
  final RxList<String> carMakers = <String>['All'].obs;
  final RxString appBarTitle = 'Results'.obs;

  final UserController userController = Get.put(UserController());

  @override
  void onInit() {
    super.onInit();
    fetchCarMakers();
    fetchLeaderboards();
  }

  void updateFilters(String newSortBy, String newScoringType) {
    sortBy.value = newSortBy;
    currentScoringType.value = newScoringType;
    //fetchLeaderboards();
  }

  void updateSortBy(String newSortBy) {
    sortBy.value = newSortBy;
    //fetchLeaderboards();
  }

  String getScoreTypeDisplayText() {
    switch (currentScoringType.value) {
      case 'best_time':
        return 'Best Tm';
      case 's1':
        return 'Sector 1';
      case 's2':
        return 'Sector 2';
      case 's3':
        return 'Sector 3';
      case 's4':
        return 'Sector 4';
      case 'speed':
        return 'Top Speed';
      default:
        return 'Best Tm';
    }
  }

  String getScoreTypeDisplayValue(ResultPosition value) {
    switch (currentScoringType.value) {
      case 'best_time':
        return value.time ?? '-';
      case 's1':
        return value.s1 ?? '-';
      case 's2':
        return value.s2 ?? '-';
      case 's3':
        return value.s3 ?? '-';
      case 's4':
        return value.s4 ?? '-';
      case 'speed':
        return value.speed ?? '-';
      default:
        return value.time ?? '-';
    }
  }

  void updateScoringType(String type) {
    currentScoringType.value = type;
  }

  void applyFilters() {
    currentScoringType.value = tempScoringType.value;
    showScoringPopup.value = false;

    fetchLeaderboards();
  }

  void applyCarMakerFilter() {
    showCarMakerFilter.value = false;

    // Update the app bar title based on car maker filter
    if (selectedCarMakers.contains('all') && selectedCarMakers.length == 1) {
      // If only 'all' is selected, don't show filter in title
      appBarTitle.value = 'Results';
    } else {
      // Show only selected car makers in title with proper casing
      List<String> properCasedMakers = [];
      for (String selected in selectedCarMakers.where((m) => m != 'all')) {
        // Find the matching car maker with proper casing
        String properCased = carMakers.firstWhere(
            (maker) => maker.toLowerCase() == selected.toLowerCase(),
            orElse: () =>
                selected // Fallback to the selected value if not found
            );
        properCasedMakers.add(properCased);
      }
      String makerFilter = properCasedMakers.join(', ');
      appBarTitle.value = 'Results: $makerFilter';
    }

    fetchLeaderboards();
  }

  Future<void> fetchLeaderboards() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      debugPrint(
          'Fetching leaderboards with scoring type: ${currentScoringType.value}');
      debugPrint('Selected car makers: $selectedCarMakers');

      var responseStr = await ApiClient.leaderboards(
          currentScoringType.value, // Will be 'best_time' by default
          selectedCarMakers);

      debugPrint('API Response received, length: ${responseStr.length}');

      // Check if the response is valid JSON
      try {
        final jsonData = jsonDecode(responseStr);
        debugPrint('JSON decoded successfully');
        debugPrint(
            'Events count in JSON: ${(jsonData['events'] as List?)?.length ?? 0}');
        debugPrint(
            'Overall positions count in JSON: ${(jsonData['overall_positions'] as List?)?.length ?? 0}');
        debugPrint(
            'Overall positions all count in JSON: ${(jsonData['overall_positions_all'] as List?)?.length ?? 0}');

        // Parse the response using the model class
        var leaderboardData = LeaderboardResponse.fromJson(jsonData);

        debugPrint('LeaderboardResponse created successfully');
        debugPrint('Events count in model: ${leaderboardData.events.length}');
        debugPrint(
            'Overall positions count in model: ${leaderboardData.overallPositions.length}');
        debugPrint(
            'Overall positions all count in model: ${leaderboardData.overallPositionsAll.length}');

        // Update the lists with data from the model
        eventList.clear();
        eventList.value = leaderboardData.events;

        resultPositionList.clear();
        resultPositionList.value = leaderboardData.overallPositions;

        resultPositionAllList.clear();
        resultPositionAllList.value = leaderboardData.overallPositionsAll;

        debugPrint('Controller lists updated');
        debugPrint('eventList count: ${eventList.length}');
        debugPrint('resultPositionList count: ${resultPositionList.length}');
        debugPrint(
            'resultPositionAllList count: ${resultPositionAllList.length}');
      } catch (jsonError) {
        debugPrint('Error parsing JSON: $jsonError');
        // Don't throw here, just log the error and try to recover
        try {
          // Try to extract at least the events list if possible
          final jsonData = jsonDecode(responseStr);
          if (jsonData['events'] != null && jsonData['events'] is List) {
            debugPrint('Attempting to recover events list only');
            List<dynamic> eventsJson = jsonData['events'];
            List<Event> events = [];

            // Process each event individually
            for (var eventJson in eventsJson) {
              try {
                Event event = Event.fromJson(eventJson);
                events.add(event);
              } catch (e) {
                debugPrint('Error parsing individual event: $e');
              }
            }

            // Update at least the events list
            eventList.clear();
            eventList.value = events;
            debugPrint('Recovered ${events.length} events');
          }
        } catch (recoveryError) {
          debugPrint('Recovery attempt failed: $recoveryError');
        }
      }

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint('Error in fetchLeaderboards: $e');
      isLoading.value = false;
      EasyLoading.dismiss();
      // Show a user-friendly error message instead of throwing
      SnackHelper.showError(
          'Unable to load leaderboard data. Please try again later.');
    }
  }

  Future<void> fetchCarMakers() async {
    try {
      String token = userController.getToken();
      int? eventId; // Set to your event ID if needed

      final response = await ApiClient.getCarMakers(token, eventId);
      final decodedResponse = json.decode(response);

      if (decodedResponse['success'] == true &&
          decodedResponse['makesWithModels'] != null) {
        // Extract names from makesWithModels
        List<dynamic> makesWithModels = decodedResponse['makesWithModels'];
        Set<String> uniqueModels = makesWithModels
            .map<String>((model) => model['name'].toString().trim())
            .toSet();

        // Convert back to List, add 'All', and sort
        List<String> models = ['All', ...uniqueModels]..sort();

        carMakers.value = models;
      } else {
        debugPrint('Invalid response format: $decodedResponse');
        carMakers.value = ['All'];
      }
    } catch (e) {
      debugPrint('Error fetching car makers: $e');
      carMakers.value = ['All'];
    }
  }
}
