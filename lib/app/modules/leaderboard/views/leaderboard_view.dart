import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/modules/shared/custom_popup.dart';
import 'package:contained_tab_bar_view/contained_tab_bar_view.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Add this import

import 'package:get/get.dart';

import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
// import '../../../routes/app_pages.dart'; // Remove this import
import '../../../routes/app_router.dart'; // Add this import
import '../../shared/support_button.dart';
import '../controllers/leaderboard_controller.dart';

class LeaderboardView extends StatelessWidget {
  LeaderboardView({super.key});

  final LeaderboardController controller = Get.put(LeaderboardController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
          icon: const Icon(Icons.menu),
          iconSize: 30,
          color: Colors.black,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () {
              controller.showScoringPopup.value = true;
              controller.showCarMakerFilter.value = false;
            },
          ),
          IconButton(
            icon: const Icon(Icons.directions_car),
            onPressed: () {
              controller.showCarMakerFilter.value = true;
              controller.showScoringPopup.value = false;
            },
          ),
          IconButton(
            padding: const EdgeInsets.only(right: 10, left: 0),
            onPressed: () {
              GoRouter.of(context).push(AppRoutePaths.notification);
            },
            icon: Obx(() {
              return Stack(
                children: [
                  const Icon(Icons.notifications,
                      color: Colors.black, size: 30),
                  (controller.userController.unreadNotificationCount.value > 0)
                      ? Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6)),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            ),
                            child: Text(
                              (controller.userController.unreadNotificationCount
                                          .value >
                                      99)
                                  ? "99+"
                                  : controller.userController
                                      .unreadNotificationCount.value
                                      .toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                      : const SizedBox()
                ],
              );
            }),
          ),
        ],
        title: Obx(() =>
            Text(controller.appBarTitle.value, style: AppTextStyles.titleText)),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return Stack(
          children: [
            ContainedTabBarView(
              tabs: const [
                Text('This Year Overall'),
                Text('All-Time Overall'),
                Text('By Event'),
              ],
              views: [
                RefreshIndicator(
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      controller.fetchLeaderboards();
                    });
                  },
                  child: ListView.builder(
                      scrollDirection: Axis.vertical,
                      itemCount: controller.resultPositionList.length + 1,
                      itemBuilder: (BuildContext context, int itemIndex) {
                        return GestureDetector(
                          onTap: () {},
                          child: buildItemListOverall(itemIndex),
                        );
                      }),
                ),
                RefreshIndicator(
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      controller.fetchLeaderboards();
                    });
                  },
                  child: ListView.builder(
                      scrollDirection: Axis.vertical,
                      itemCount: controller.resultPositionAllList.length + 1,
                      itemBuilder: (BuildContext context, int itemIndex) {
                        return GestureDetector(
                          onTap: () {},
                          child: buildItemListOverallAll(itemIndex),
                        );
                      }),
                ),
                RefreshIndicator(
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      controller.fetchLeaderboards();
                    });
                  },
                  child: ListView.builder(
                      scrollDirection: Axis.vertical,
                      itemCount: controller.eventList.length,
                      itemBuilder: (BuildContext context, int itemIndex) {
                        // Get the event from the controller's eventList (populated from LeaderboardResponse)
                        final event = controller.eventList[itemIndex];

                        return GestureDetector(
                          onTap: () {
                            GoRouter.of(context).push(
                                AppRoutePaths.leaderboardDetail,
                                extra: event);
                          },
                          child: buildItemListByEvent(itemIndex),
                        );
                      }),
                )
              ],
              tabBarProperties: const TabBarProperties(
                indicatorColor: AppColor.kTextColor,
                labelColor: AppColor.kTextColor,
              ),
              onChange: (index) {},
            ),

            // Scoring options popup
            if (controller.showScoringPopup.value) buildScoringPopup(),
            // Car maker filter popup
            if (controller.showCarMakerFilter.value)
              buildCarMakerFilterPopup(context),
          ],
        );
      }),
    );
  }

  Widget buildScoringOptionButton(String title, String value) {
    return Obx(() => OutlinedButton(
          onPressed: () {
            controller.tempScoringType.value =
                value; // Store in temp variable instead
          },
          style: OutlinedButton.styleFrom(
            backgroundColor: controller.tempScoringType.value ==
                    value // Use temp variable for highlighting
                ? AppColor.primaryButtonColor.withAlpha(26)
                : Colors.transparent,
            side: BorderSide.none,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            title,
            style: const TextStyle(
              color: AppColor.primaryButtonColor,
              fontSize: 14,
            ),
          ),
        ));
  }

  Widget buildCarMakerButton(String title, String value) {
    return Obx(() => OutlinedButton(
          onPressed: () {
            // For single selection, just reset the array and set the new value
            if (value == "all") {
              controller.selectedCarMakers.value = ["all"];
            } else if (controller.selectedCarMakers.length == 1 &&
                controller.selectedCarMakers.contains(value)) {
              // If clicking the only selected button, reset to "all"
              controller.selectedCarMakers.value = ["all"];
            } else {
              // Set only this value
              controller.selectedCarMakers.value = [value];
            }
          },
          style: OutlinedButton.styleFrom(
            backgroundColor: controller.selectedCarMakers.contains(value)
                ? AppColor.primaryButtonColor.withAlpha(26)
                : Colors.transparent,
            side: BorderSide(
              width: 1.0,
              color: AppColor.primaryButtonColor.withAlpha(26),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            title,
            style: const TextStyle(
              color: AppColor.primaryButtonColor,
              fontSize: 14,
            ),
          ),
        ));
  }

  Widget buildCarMakerFilterPopup(BuildContext context) {
    return CustomPopup(
      onTap: () {
        controller.showCarMakerFilter.value = false;
        controller.showScoringPopup.value = false;
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20),
          Text('Filter by Make / Model',
              style: AppTextStyles.normalText.copyWith(fontSize: 18)),
          const SizedBox(height: 20),
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.5,
              minHeight: 50, // Minimum height for content
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: controller.carMakers
                      .map((maker) =>
                          buildCarMakerButton(maker, maker.toLowerCase()))
                      .toList(),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  controller.applyCarMakerFilter();
                  controller.showCarMakerFilter.value = false;
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.primaryButtonColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Apply',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildScoringPopup() {
    return CustomPopup(
      onTap: () {
        controller.showScoringPopup.value = false;
        controller.showCarMakerFilter.value = false;
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20),
          Text('Sort By',
              style: AppTextStyles.normalText.copyWith(fontSize: 18)),
          const SizedBox(height: 20),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: GridView.count(
              shrinkWrap: true,
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 2.5,
              children: [
                buildScoringOptionButton('Best Time', 'best_time'),
                buildScoringOptionButton('Top Speed', 'speed'),
                buildScoringOptionButton('Sector 1', 's1'),
                buildScoringOptionButton('Sector 2', 's2'),
                buildScoringOptionButton('Sector 3', 's3'),
                buildScoringOptionButton('Sector 4', 's4'),
              ],
            ),
          ),
          const SizedBox(height: 30),

          // Apply button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  controller.applyFilters();
                  controller.showScoringPopup.value = false;
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.primaryButtonColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Apply',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  buildItemListByEvent(int itemIndex) {
    // Access the event from the controller's eventList, which is populated from LeaderboardResponse
    final event = controller.eventList[itemIndex];

    return Padding(
      padding: const EdgeInsets.only(
          left: AppConfig.defaultPadding,
          right: AppConfig.defaultPadding,
          top: AppConfig.defaultPadding / 2,
          bottom: AppConfig.defaultPadding / 2),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: AppConfig.defaultPadding,
                  right: AppConfig.defaultPadding,
                  top: AppConfig.defaultPadding,
                  bottom: AppConfig.defaultPadding / 2),
              child: Text(event.title ?? "",
                  maxLines: 3,
                  style: AppTextStyles.smallTitleBold,
                  overflow: TextOverflow.ellipsis),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  left: AppConfig.defaultPadding,
                  right: AppConfig.defaultPadding,
                  bottom: AppConfig.defaultPadding),
              child: Text(
                event.eventDate != null
                    ? StringUtil.dateTimeToString(event.eventDate!)
                    : 'Date not available',
                style: AppTextStyles.smallText.copyWith(color: Colors.black),
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildItemListOverall(int index) {
    int itemIndex = index - 1;
    return (index == 0)
        ? Padding(
            padding: const EdgeInsets.only(
              left: AppConfig.defaultPadding,
              right: AppConfig.defaultPadding,
              top: AppConfig.defaultPadding / 2,
              //bottom: AppConfig.defaultPadding / 2
            ),
            child: Card(
                elevation: 0,
                color: Colors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 13),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Pos",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 13,
                        ),
                        Text(
                          "Name",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const Spacer(),
                        Text(
                          controller.getScoreTypeDisplayText(),
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                      ],
                    ),
                  ],
                )),
          )
        : Padding(
            padding: const EdgeInsets.only(
                left: AppConfig.defaultPadding,
                right: AppConfig.defaultPadding,
                top: AppConfig.defaultPadding / 2,
                bottom: AppConfig.defaultPadding / 2),
            child: Card(
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller.resultPositionList[itemIndex].position == 0)
                            ? "-"
                            : controller.resultPositionList[itemIndex].position
                                .toString(),
                        style: AppTextStyles.titleText
                            .copyWith(color: Colors.black, fontSize: 20),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: AppConfig.defaultPadding,
                            right: AppConfig.defaultPadding,
                            top: AppConfig.defaultPadding,
                            bottom: AppConfig.defaultPadding / 2),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                controller.resultPositionList[itemIndex]
                                        .userName ??
                                    "",
                                maxLines: 1,
                                style: AppTextStyles.smallTitleBold
                                    .copyWith(fontSize: 16),
                                overflow: TextOverflow.ellipsis),
                            (controller.resultPositionList[itemIndex].vehicle ==
                                    "  ")
                                ? const SizedBox(height: 2)
                                : const SizedBox(height: 5),
                            (controller.resultPositionList[itemIndex].vehicle ==
                                    "  ")
                                ? Container()
                                : Text(
                                    controller.resultPositionList[itemIndex]
                                            .vehicle ??
                                        "",
                                    maxLines: 3,
                                    style: AppTextStyles.normalText,
                                    overflow: TextOverflow.ellipsis),
                          ],
                        ),
                      ),
                    ),
                    //Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        controller.getScoreTypeDisplayValue(
                            controller.resultPositionList[itemIndex]),
                        style: AppTextStyles.normalText
                            .copyWith(color: Colors.black, fontSize: 12),
                      ),
                    ),
                  ],
                )),
          );
  }

  buildItemListOverallAll(int index) {
    int itemIndex = index - 1;
    return (index == 0)
        ? Padding(
            padding: const EdgeInsets.only(
              left: AppConfig.defaultPadding,
              right: AppConfig.defaultPadding,
              top: AppConfig.defaultPadding / 2,
              //bottom: AppConfig.defaultPadding / 2
            ),
            child: Card(
                elevation: 0,
                color: Colors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 13),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Pos",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 13,
                        ),
                        Text(
                          "Name",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const Spacer(),
                        Text(
                          controller.getScoreTypeDisplayText(),
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                      ],
                    ),
                  ],
                )),
          )
        : Padding(
            padding: const EdgeInsets.only(
                left: AppConfig.defaultPadding,
                right: AppConfig.defaultPadding,
                top: AppConfig.defaultPadding / 2,
                bottom: AppConfig.defaultPadding / 2),
            child: Card(
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller.resultPositionAllList[itemIndex].position ==
                                0)
                            ? "-"
                            : controller
                                .resultPositionAllList[itemIndex].position
                                .toString(),
                        style: AppTextStyles.titleText
                            .copyWith(color: Colors.black, fontSize: 20),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: AppConfig.defaultPadding,
                            right: AppConfig.defaultPadding,
                            top: AppConfig.defaultPadding,
                            bottom: AppConfig.defaultPadding / 2),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                controller.resultPositionAllList[itemIndex]
                                        .userName ??
                                    "",
                                maxLines: 1,
                                style: AppTextStyles.smallTitleBold
                                    .copyWith(fontSize: 16),
                                overflow: TextOverflow.ellipsis),
                            (controller.resultPositionAllList[itemIndex]
                                        .vehicle ==
                                    "  ")
                                ? const SizedBox(height: 2)
                                : const SizedBox(height: 5),
                            (controller.resultPositionAllList[itemIndex]
                                        .vehicle ==
                                    "  ")
                                ? Container()
                                : Text(
                                    controller.resultPositionAllList[itemIndex]
                                            .vehicle ??
                                        "",
                                    maxLines: 3,
                                    style: AppTextStyles.normalText,
                                    overflow: TextOverflow.ellipsis),
                          ],
                        ),
                      ),
                    ),
                    //Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        controller.getScoreTypeDisplayValue(
                            controller.resultPositionAllList[itemIndex]),
                        style: AppTextStyles.normalText
                            .copyWith(color: Colors.black, fontSize: 12),
                      ),
                    ),
                  ],
                )),
          );
  }
}
