import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:automoment/app/helpers/debug_helper.dart';
import 'package:automoment/app/modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart'; // Keep Get for controller and Get.context!
import '../../../controllers/base_screen_controller.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../constants/app_config.dart';
import '../../../controllers/user_controller.dart';
import '../../../helpers/notification_util.dart';
import '../../../models/addon_model.dart';
import '../../../models/driver_lookup_model.dart';
import '../../../models/event_modal.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../../services/api_client.dart';

class EventsDetailController extends BaseScreenController {
  @override
  String get screenName => 'Event Detail';

  var event = Event().obs;
  late WebViewController webViewController;
  var webViewHeight = 300.0.obs;
  var isRegistered = false.obs;
  var isLoading = true.obs;
  var bookingStatus = 0.obs;
  var isLoadingSignedForm = false.obs;
  var isFormSigned = false.obs;
  var isExternalEvent = true.obs;
  int notificationId = 0;
  var eventInfoLoaded = false.obs;

  var pitNo = "".obs;
  var vehicle = "".obs;
  var qrcode = "".obs;
  var mainDriver = "".obs;

  var addonsNotRequireBooking = <Addon>[].obs;
  var showPopupRegister = false.obs;

  final UserController userController = Get.put(UserController());

  var registrationType = "".obs;

  var mainDrivers = <MainDriver>[].obs;
  var selectedMainDriver = MainDriver().obs;
  var filteredDrivers = <MainDriver>[].obs;
  var searchQuery = ''.obs;
  var mainDriverVehicle = "".obs;
  var isRebateAvailable = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();

    initWebViewController();

    var data = Get.arguments;

    if (data is String) {
      debugPrint("from notification or deeplink");

      String json = data;
      int eventId = -1;
      try {
        eventId = jsonDecode(json)['id'];
        notificationId = jsonDecode(json)['notificationId'];
      } catch (e) {
        debugPrint(e as String?);
      }

      debugPrint("eventId: $eventId");

      if (eventId == -1) {
        DebugHelper.d("from deeplink");

        try {
          eventId = int.parse(data);
        } catch (e) {
          debugPrint(e as String?);
        }

        await fetchEvent(eventId);
      } else {
        debugPrint("from notification");
        await fetchEvent(eventId);
        if (userController.isLoggedIn.value) {
          NotificationUtil.callMarkAsRead(userController.getToken(),
              userController.getUser().id!, notificationId);
        }
      }
    } else {
      debugPrint("from event");
      event.value = data[0];

      await fetchEvent(data[0].id!);

      //isExternalEvent.value = event.value.isExternalEvent!;
      //debugPrint("isExternalEvent: ${isExternalEvent.value}");
      //checkStatus();
    }

    await loadEventInfo();
  }

  initWebViewController() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {
            DebugHelper.d("onPageStarted: $url");
            EasyLoading.show(status: 'Please wait...');
          },
          onPageFinished: (String url) async {
            DebugHelper.d("onPageFinished: $url");

            final result = await webViewController.runJavaScriptReturningResult(
                "document.documentElement.scrollHeight;");
            double height = double.tryParse(result.toString()) ?? 300.0;
            webViewHeight.value = height;

            EasyLoading.dismiss();
          },
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) async {
            DebugHelper.d("navigationDelegate: ${request.url}");

            if (Platform.isIOS) {
              if (request.url
                      .startsWith("https://app.automoment.com.sg/event/") ||
                  request.url
                      .startsWith("https://testapp.automoment.com.sg/event/") ||
                  request.url.startsWith("https://automoment.test/event/")) {
                return NavigationDecision.navigate;
              }

              if (request.url.startsWith('https://youtube.com') ||
                  request.url.startsWith('https://www.youtube.com') ||
                  request.url.startsWith('https://youtu.be') ||
                  request.url.startsWith('https://www.youtu.be')) {
                return NavigationDecision.navigate;
              }
            }

            if (request.url.endsWith(".pdf")) {
              // open external app
              if (!await launchUrl(
                Uri.parse("https://docs.google.com/viewer?url=${request.url}"),
                mode: LaunchMode.externalApplication,
              )) {
                throw 'Could not launch ${request.url}';
              }
              return NavigationDecision.prevent;
            }

            // open external browser
            if (request.url.startsWith("http")) {
              if (!await launchUrl(
                Uri.parse(request.url),
                mode: LaunchMode.externalApplication,
              )) {
                throw 'Could not launch ${request.url}';
              }
              return NavigationDecision.prevent;
            }

            return NavigationDecision.prevent;
          },
        ),
      );
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();

    await loadEventInfo();
  }

  setWebViewHeight(double h) {
    webViewHeight.value = h;
  }

  Future<void> fetchIsRegistered() async {
    try {
      debugPrint(
          "fetchIsRegistered ${userController.user.value.id} ${event.value.id}");
      var response = await ApiClient.isRegistered(userController.getToken(),
          userController.user.value.id!, event.value.id!);
      isRegistered.value = jsonDecode(response)['success'];
      isLoading.value = false;
      debugPrint("isRegistered: ${isRegistered.value}");
      debugPrint("isLoading: ${isLoading.value}");
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<void> fetchCheckBookingStatus() async {
    try {
      isLoading.value = true;
      var response = await ApiClient.checkBookingStatus(
          userController.getToken(),
          userController.user.value.id!,
          event.value.id!);
      bookingStatus.value = jsonDecode(response)['status'];
      debugPrint(
          "bookingStatus: ${bookingStatus.value} ${jsonDecode(response)['message']}");
      isLoading.value = false;

      if (bookingStatus.value == 2 || bookingStatus.value == 3) {
        registrationType.value = "Driver";
        pitNo.value = jsonDecode(response)['pit_no'];
        vehicle.value = jsonDecode(response)['vehicle'];
        qrcode.value = jsonDecode(response)['qrcode'];
        isRebateAvailable.value = jsonDecode(response)['is_rebate_available'];
        if (vehicle.value == "") {
          showDialog(
              context: Get.context!,
              builder: (context) {
                return KMessageDialogView(
                  content:
                      "You haven't selected your vehicle. Please select your vehicle first.",
                  callback: () {
                    Navigator.of(Get.context!).pop(); // Changed from Get.back()
                    AppRouter.router.push(AppRoutePaths.updateMyBooking,
                        extra: event.value);
                  },
                );
              });
        } else if ((pitNo.value == "-" || pitNo.value == "") &&
            event.value.isRequiredPitNumber!) {
          showDialog(
              context: Get.context!,
              builder: (context) {
                return KMessageDialogView(
                  content:
                      "You haven't selected your pit. Please select your pit first.",
                  callback: () {
                    Navigator.of(Get.context!).pop(); // Changed from Get.back()
                    AppRouter.router.push(AppRoutePaths.updateMyBooking,
                        extra: event.value);
                  },
                );
              });
        }
      } else if (bookingStatus.value == 7) {
        registrationType.value = "Passenger";
        qrcode.value = jsonDecode(response)['qrcode'];
      } else if (bookingStatus.value == 9) {
        registrationType.value = "Additional Driver";
        mainDriver.value = jsonDecode(response)['main_driver'];
        vehicle.value = jsonDecode(response)['vehicle'];
        mainDriverVehicle.value = jsonDecode(response)['main_driver_vehicle'];
        pitNo.value = jsonDecode(response)['pit_no'];
        qrcode.value = jsonDecode(response)['qrcode'];
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<void> fetchCheckBookingStatusFromPaymentScreen() async {
    try {
      isLoading.value = true;
      var response = await ApiClient.checkBookingStatus(
          userController.getToken(),
          userController.user.value.id!,
          event.value.id!);
      bookingStatus.value = jsonDecode(response)['status'];
      debugPrint(
          "fetchCheckBookingStatusFromPaymentScreen: ${bookingStatus.value} ${jsonDecode(response)['message']}");
      isLoading.value = false;

      if (bookingStatus.value == 2 || bookingStatus.value == 3) {
        registrationType.value = "Driver";
        pitNo.value = jsonDecode(response)['pit_no'];
        vehicle.value = jsonDecode(response)['vehicle'];
        qrcode.value = jsonDecode(response)['qrcode'];

        if (vehicle.value == "") {
          showDialog(
              context: Get.context!,
              builder: (context) {
                return KMessageDialogView(
                  content:
                      "You haven't selected your vehicle. Please select your vehicle first.",
                  callback: () {
                    Navigator.of(Get.context!).pop(); // Changed from Get.back()
                    AppRouter.router.push(AppRoutePaths.updateMyBooking,
                        extra: event.value);
                  },
                );
              });
        } else if (pitNo.value == "-" && event.value.isRequiredPitNumber!) {
          showDialog(
              context: Get.context!,
              builder: (context) {
                return KMessageDialogView(
                  content:
                      "You haven't selected your pit. Please select your pit first.",
                  callback: () {
                    Navigator.of(Get.context!).pop(); // Changed from Get.back()
                    AppRouter.router.push(AppRoutePaths.updateMyBooking,
                        extra: event.value);
                  },
                );
              });
        }
      } else if (bookingStatus.value == 7) {
        registrationType.value = "Passenger";
        qrcode.value = jsonDecode(response)['qrcode'];
      } else if (bookingStatus.value == 9) {
        registrationType.value = "Additional Driver";
        mainDriver.value = jsonDecode(response)['main_driver'];
        vehicle.value = jsonDecode(response)['vehicle'];
        mainDriverVehicle.value = jsonDecode(response)['main_driver_vehicle'];
        pitNo.value = jsonDecode(response)['pit_no'];
        qrcode.value = jsonDecode(response)['qrcode'];
      }

      await loadEventInfo();
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<void> fetchIsEventFormSigned() async {
    try {
      String response = '{"success": false}';
      if (registrationType.value == "Driver") {
        response = await ApiClient.isEventFormSigned(
            userController.getToken(), event.value.id!);
      } else if (registrationType.value == "Passenger") {
        response = await ApiClient.isEventFormSignedPassenger(
            userController.getToken(), event.value.id!);
      }

      isFormSigned.value = jsonDecode(response)['success'];
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<void> fetchEvent(int id) async {
    try {
      EasyLoading.show(status: 'loading...');
      var response = await ApiClient.getEventFromId(id);
      EasyLoading.dismiss();
      event.value = Event.fromJson(jsonDecode(response)['data']);

      addonsNotRequireBooking.value = event.value.addonsNotRequireBooking!;

      isExternalEvent.value = event.value.isExternalEvent!;
      debugPrint("isExternalEvent: ${isExternalEvent.value}");
      checkStatus();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> loadEventInfo() async {
    if (event.value.id != null) {
      await webViewController.loadRequest(Uri.parse(
          "${AppConfig.webUrl}event/${event.value.id}?${randomString(10)}"));
    }
  }

  Future<void> checkStatus() async {
    isLoading.value = true;
    await fetchCheckBookingStatus();
    await fetchIsEventFormSigned();
  }

  String randomString(int length) {
    var r = Random();
    const chars = '0123456789';
    return List.generate(length, (index) => chars[r.nextInt(chars.length)])
        .join();
  }

  Future<void> fetchMainDrivers() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      final response = await ApiClient.lookupMainDriver(
          userController.getToken(), event.value.id!);
      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'];
      if (success) {
        var list = jsonDecode(response)['mainDrivers'] as List<dynamic>;
        mainDrivers.value = list
            .map((e) => MainDriver.fromJson(e as Map<String, dynamic>))
            .toList()
            .cast<MainDriver>();
        //var list = jsonDecode(response)['mainDrivers'];
        //mainDrivers.value = list.map((e) => MainDriver.fromJson(e)).toList();
        //debugPrint(list);
        //mainDrivers.value = jsonDecode(response)['mainDrivers'].map((e) => MainDriver.fromJson(e)).toList();
      }
    } catch (e) {
      debugPrint('Error fetching drivers: $e');
    } finally {
      EasyLoading.dismiss();
    }
  }

  void filterDrivers(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredDrivers.assignAll(mainDrivers);
    } else {
      filteredDrivers.assignAll(mainDrivers.where((driver) =>
          driver.name!.toLowerCase().contains(query.toLowerCase()) ||
          driver.mobileNumber!.toLowerCase().contains(query.toLowerCase()) ||
          driver.email!.toLowerCase().contains(query.toLowerCase())));
    }
  }

  Future<void> cancelNoPaymentDriverRegistration() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      await ApiClient.cancelNoPaymentDriverRegistration(
          userController.getToken(), event.value.id!);
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> cancelNoPaymentAdditionalDriverRegistration() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      await ApiClient.cancelNoPaymentAdditionalDriverRegistration(
          userController.getToken(), event.value.id!);
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> cancelNoPaymentPassengerRegistration() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      await ApiClient.cancelNoPaymentPassengerRegistration(
          userController.getToken(), event.value.id!);
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
