import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:automoment/app/modules/events_detail/views/program_view.dart';
import 'package:automoment/app/modules/events_detail/views/ticket_view.dart';
import 'package:automoment/app/modules/events_detail/views/user_lookup_view.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Import GoRouter for context.push()

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:share_plus/share_plus.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../controllers/nav_history_controller.dart';
import '../../../models/user_model.dart';
import '../../../routes/app_router.dart';
import '../../shared/custom_popup.dart';
import '../../shared/support_button.dart';
import '../controllers/events_detail_controller.dart';
import '../../../controllers/user_controller.dart';
import 'event_info_view.dart';

class EventsDetailView extends StatelessWidget {
  EventsDetailView({super.key});

  final EventsDetailController controller = Get.put(EventsDetailController());
  final UserController userController = Get.put(UserController());
  final NavHistoryController navHistoryController =
      Get.put(NavHistoryController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColor.kLightBgColor,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          title: const Text(
            "",
            style: TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
          elevation: 1,
          actions: [
            GestureDetector(
              onTap: () {
                debugPrint("ontap");
                final box = context.findRenderObject() as RenderBox?;
                double w = box?.size.width ?? 820;
                Share.share(
                    '${controller.event.value.title} at ${controller.event.value.firebaseDynamicLink}',
                    sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
              },
              child: const Padding(
                  padding: EdgeInsets.only(right: 20),
                  child: Icon(
                    Icons.share,
                    color: Colors.white,
                  )),
            ),
          ],
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(
          () => controller.event.value.id == null
              ? Container(
                  color: Colors.black,
                )
              : Stack(
                  children: [
                    // main view
                    ListView(padding: const EdgeInsets.only(top: 0), children: [
                      Stack(children: [
                        Stack(children: [
                          // img background on top
                          Container(
                            padding: const EdgeInsets.only(top: 0),
                            width: (Get.width),
                            height: 230,
                            margin: const EdgeInsets.all(0.0),
                            child: (controller.event.value.id == null)
                                ? Container()
                                : FadeInImage(
                                    placeholder: const AssetImage(
                                        'assets/images/image_placeholder.png'),
                                    image: NetworkImage(
                                        "${AppConfig.storageUrl}${controller.event.value.image}"),
                                    fit: BoxFit.cover,
                                    imageErrorBuilder:
                                        (context, error, stackTrace) {
                                      return const Image(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/images/image_placeholder.png'),
                                      );
                                    },
                                  ),
                          ),

                          // content
                          Container(
                            padding: const EdgeInsets.only(
                                top: 180,
                                bottom: AppConfig.defaultPadding * 2,
                                left: AppConfig.defaultPadding * 2,
                                right: AppConfig.defaultPadding * 2),
                            child: Column(
                              children: [
                                (controller.bookingStatus.value == 2 ||
                                        controller.bookingStatus.value == 3)
                                    ? TicketView(
                                        event: controller.event.value,
                                        qrcode: controller.qrcode.value,
                                        pitNo: controller.pitNo.value,
                                        vehicle: controller.vehicle.value,
                                        registrationType:
                                            controller.registrationType.value,
                                        mainDriver: "",
                                        mainDriverVehicle: "",
                                      )
                                    : Container(),
                                (controller.bookingStatus.value == 7)
                                    ? TicketView(
                                        event: controller.event.value,
                                        qrcode: controller.qrcode.value,
                                        pitNo: "-",
                                        vehicle: ".",
                                        registrationType:
                                            controller.registrationType.value,
                                        mainDriver: "",
                                        mainDriverVehicle: "",
                                      )
                                    : Container(),
                                (controller.bookingStatus.value == 9)
                                    ? TicketView(
                                        event: controller.event.value,
                                        qrcode: controller.qrcode.value,
                                        pitNo: controller.pitNo.value,
                                        vehicle: controller.vehicle.value,
                                        registrationType:
                                            controller.registrationType.value,
                                        mainDriver: controller.mainDriver.value,
                                        mainDriverVehicle:
                                            controller.mainDriverVehicle.value,
                                      )
                                    : Container(),
                                if (controller.bookingStatus.value == 2 ||
                                    controller.bookingStatus.value == 3 ||
                                    controller.bookingStatus.value == 7 ||
                                    controller.bookingStatus.value == 9)
                                  ProgramView(
                                      event: controller.event.value,
                                      vehicle: controller.vehicle.value),
                                EventInfoView(),
                                const SizedBox(height: 40),
                                if (!controller.isExternalEvent.value) ...[
                                  (controller.bookingStatus.value == 2 ||
                                          controller.bookingStatus.value == 3)
                                      ? updateMyBookingButton(context)
                                      : Container(),
                                  (controller.addonsNotRequireBooking
                                              .isNotEmpty ||
                                          controller.bookingStatus.value == 2 ||
                                          controller.bookingStatus.value == 3)
                                      ? addOnPurchaseButton(context)
                                      : Container(),
                                  (controller.bookingStatus.value == 2 ||
                                          controller.bookingStatus.value == 3 ||
                                          controller.bookingStatus.value == 7 ||
                                          controller.bookingStatus.value == 9)
                                      ? (controller.isFormSigned.value
                                          ? viewSignedFormButton(context)
                                          : viewUnsignedFormButton(context))
                                      : const SizedBox(),
                                  (controller.bookingStatus.value == 4 ||
                                          controller.bookingStatus.value == 8 ||
                                          controller.bookingStatus.value == 10)
                                      ? payNowButton(context)
                                      : Container(),
                                  (controller.bookingStatus.value == 2)
                                      ? checkInNowButton(context)
                                      : Container(),
                                  (controller.bookingStatus.value == 3)
                                      ? alreadyCheckedInButton(context)
                                      : Container(),
                                  (controller.bookingStatus.value == 6)
                                      ? joinWaitingListButton(context)
                                      : Container(),
                                  (controller.bookingStatus.value == 5)
                                      ? joinedWaitingListButton(context)
                                      : Container(),
                                  (controller.bookingStatus.value == 1)
                                      ? bookNowButton(context)
                                      : Container(),
                                  ((controller.bookingStatus.value == 2 ||
                                              controller.bookingStatus.value ==
                                                  3) &&
                                          controller.isRebateAvailable.value)
                                      ? claimRebateButton(context)
                                      : Container(),
                                  const SizedBox(height: 20),
                                ]
                              ],
                            ),
                          ),
                        ]),
                      ]),
                    ]),

                    // popup menu
                    if (controller.showPopupRegister.value)
                      CustomPopup(
                        onTap: () {
                          debugPrint("onTap");
                          controller.showPopupRegister.value = false;
                        },
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(height: 20),
                            Text('Choose Registration Type',
                                style: AppTextStyles.normalText
                                    .copyWith(fontSize: 18)),
                            const SizedBox(height: 30),
                            driverRegistrationButton(context),
                            if (controller.event.value.additionalDriver!)
                              additionalDriverRegistrationButton(context),
                            if (controller.event.value.passenger!)
                              passengerRegistrationButton(context),
                            if (controller.event.value.group!)
                              groupRegistrationButton(context)
                          ],
                        ),
                      ),
                  ],
                ),
        ));
  }

  Widget updateMyBookingButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context).push(AppRoutePaths.updateMyBooking,
                extra: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Update My Registration",
                  style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget addOnPurchaseButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context)
                .push(AppRoutePaths.addon, extra: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Addons", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget bookNowButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            if (controller.event.value.additionalDriver! ||
                controller.event.value.passenger! ||
                controller.event.value.group!) {
              controller.showPopupRegister.value = true;
            } else {
              GoRouter.of(context)
                  .push(AppRoutePaths.booking, extra: controller.event.value);
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Register", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget joinWaitingListButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context).push(AppRoutePaths.joinWaitingList,
                extra: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Join Waiting List", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget joinedWaitingListButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor: WidgetStateProperty.all<Color>(Colors.black12),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(color: Colors.black12)))),
          onPressed: () {
            // No action
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("You have joined the waiting list",
                  style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget checkInNowButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context)
                .push(AppRoutePaths.checkIn, extra: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Check In Now", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget alreadyCheckedInButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {},
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("You already checked in",
                  style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget viewSignedFormButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context).push(AppRoutePaths.eventsFormSigned, extra: {
              "event": controller.event.value,
              "type": controller.registrationType.value.toLowerCase()
            });
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Indemnity Form", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget viewUnsignedFormButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context).push(AppRoutePaths.eventsFormSign, extra: {
              "event": controller.event.value,
              "type": controller.registrationType.value.toLowerCase()
            });
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child:
                  Text("Sign Indemnity Form", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget payNowButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            if (controller.bookingStatus.value == 8) {
              // passenger
              GoRouter.of(context).push(AppRoutePaths.makePayment, extra: {
                "event": controller.event.value,
                "type": "passenger"
              });
            } else if (controller.bookingStatus.value == 10) {
              // additional driver
              GoRouter.of(context).push(AppRoutePaths.makePayment, extra: {
                "event": controller.event.value,
                "type": "additional driver",
                "mainDriver": controller.mainDriver.value
              });
            } else {
              // driver
              GetStorage().write('fromEventDetailMakePaymentButton', true);
              GoRouter.of(context).push(AppRoutePaths.makePayment,
                  extra: controller.event.value);
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
                child: Text("Pay Now", style: TextStyle(fontSize: 16))),
          )),
    );
  }

  Widget driverRegistrationButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            controller.showPopupRegister.value = false;
            if (userController.isLoggedIn.value) {
              GoRouter.of(context).push(AppRoutePaths.booking,
                  extra: {"event": controller.event.value, "type": "driver"});
            } else {
              navHistoryController.backPage = AppRoutePaths.booking;
              navHistoryController.backPageArgs = {
                "event": controller.event.value,
                "type": "driver"
              };
              GoRouter.of(context).push(AppRoutePaths.guest);
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Driver", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget additionalDriverRegistrationButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () async {
            controller.showPopupRegister.value = false;
            await controller.fetchMainDrivers();
            controller.filteredDrivers.assignAll(controller.mainDrivers);

            // ignore: use_build_context_synchronously
            if (!context.mounted) return;

            final selectedUser = await showDialog<User>(
              context: context,
              builder: (BuildContext dialogContext) {
                return UserLookupView();
              },
            );
            if (selectedUser != null) {
              debugPrint('Selected user: ${selectedUser.name}');
              // Potentially navigate or pass data based on selectedUser
              // Example:
              // GoRouter.of(context).push(AppRoutePaths.booking, extra: {
              //   "event": controller.event.value,
              //   "type": "additional_driver",
              //   "mainDriverId": selectedUser.id
              // });
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 0, right: 0),
            height: 50,
            child: const Center(
              child: Text("Additional Driver", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget passengerRegistrationButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () async {
            controller.showPopupRegister.value = false;
            if (userController.isLoggedIn.value) {
              GoRouter.of(context).push(AppRoutePaths.booking, extra: {
                "event": controller.event.value,
                "type": "passenger"
              });
            } else {
              navHistoryController.backPage = AppRoutePaths.booking;
              navHistoryController.backPageArgs = {
                "event": controller.event.value,
                "type": "passenger"
              };
              GoRouter.of(context).push(AppRoutePaths.guest);
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Passenger", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget groupRegistrationButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            controller.showPopupRegister.value = false;
            GoRouter.of(context)
                .push(AppRoutePaths.group, extra: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Group", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget claimRebateButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 0, right: 0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            GoRouter.of(context)
                .push(AppRoutePaths.claimRebate, extra: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Claim Rebate", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }
}
