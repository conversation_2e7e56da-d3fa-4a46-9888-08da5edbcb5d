import 'package:automoment/app/helpers/debug_helper.dart';
import 'package:automoment/app/helpers/notification_util.dart';
import 'package:automoment/app/modules/account/views/account_view.dart';
import 'package:automoment/app/modules/events/views/events_view.dart';
import 'package:automoment/app/modules/news/views/news_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../home/<USER>/home_view.dart';
import '../../leaderboard/views/leaderboard_view.dart';
import '../../reward/views/reward_view.dart';
import '../../store/views/store_view.dart';

class BottombarController extends GetxController with WidgetsBindingObserver {
  // Consider initializing with a default that makes sense, e.g., 0 for HomeView if that's the first page.
  // If Get.arguments is expected to always provide the initial index, this is fine.
  var index = 1.obs;

  final UserController userController = Get.find<
      UserController>(); // Assuming UserController is registered elsewhere (e.g., in Bindings)

  @override
  Future<void> onInit() async {
    super.onInit();

    await NotificationUtil().registerTokenAndSubscribe();
    NotificationUtil().checkPushNotificationInitialMessage();

    // Ensure Get.arguments is an int if it's not null, or add type checking.
    if (Get.arguments != null) {
      index.value = Get.arguments;
    }

    if (userController.isLoggedIn.value) {
      userController.callRegisterFcmTokenApi();
      userController.callGetUnreadNotificationCountApi();
    } else {
      AppRouter.router
          .go(AppRoutePaths.guest); // Changed to AppRouter.router.go
      return; // Don't continue if not logged in
    }

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    switch (state) {
      case AppLifecycleState.resumed:
        if (userController.isLoggedIn.value) {
          // Consider if this delay is strictly necessary.
          await Future.delayed(const Duration(seconds: 1));
          userController.callGetUnreadNotificationCountApi();
        }
        break;
      case AppLifecycleState.inactive:
        DebugHelper.d("inactive");
        break;
      case AppLifecycleState.paused:
        DebugHelper.d("paused");
        break;
      case AppLifecycleState.detached:
        DebugHelper.d("detached");
        break;
      case AppLifecycleState.hidden:
        DebugHelper.d("hidden"); // Or implement specific logic
        break;
    }
  }

  final List<Widget> pageMember = [
    // Made final, explicitly typed
    HomeView(),
    NewsView(),
    EventsView(),
    LeaderboardView(),
    RewardView(),
    StoreView(),
    AccountView(),
  ];

  void changePage(int i) {
    // If 0 is a valid index (e.g., for HomeView), this condition might be problematic.
    // If the intent is to prevent re-selecting the current page, use:
    // if (i != index.value && i >= 0 && i < pageMember.length)
    // For now, assuming any valid index is allowed:
    if (i >= 0 && i < pageMember.length) {
      index.value = i;
    }
  }

  Widget getCurrentPageWidget() {
    // Renamed for clarity and removed redundant method
    return pageMember[index.value];
  }

  void chatPage() {
    AppRouter.router
        .push(AppRoutePaths.chatList); // Changed to AppRouter.router.push
  }
}
