import 'package:automoment/app/helpers/debug_helper.dart';
import 'package:automoment/app/helpers/notification_util.dart';
import 'package:automoment/app/modules/account/views/account_view.dart';
import 'package:automoment/app/modules/events/views/events_view.dart';
import 'package:automoment/app/modules/news/views/news_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../home/<USER>/home_view.dart';
import '../../leaderboard/views/leaderboard_view.dart';
import '../../reward/views/reward_view.dart';
import '../../store/views/store_view.dart';

class BottombarController extends GetxController with WidgetsBindingObserver {
  // Initialize with Home page (index 0) as default
  var index = 0.obs;

  // Use lazy getter for UserController to avoid initialization issues
  UserController get userController {
    if (Get.isRegistered<UserController>()) {
      return Get.find<UserController>();
    } else {
      return Get.put(UserController(), permanent: true);
    }
  }

  @override
  Future<void> onInit() async {
    super.onInit();

    try {
      // Initialize notification services
      await _initializeNotifications();

      // Handle navigation arguments safely
      _handleNavigationArguments();

      // Handle user authentication state
      await _handleUserAuthentication();

      // Add lifecycle observer
      WidgetsBinding.instance.addObserver(this);

      DebugHelper.d("BottombarController initialized successfully");
    } catch (e, stackTrace) {
      DebugHelper.d("Error initializing BottombarController: $e");
      DebugHelper.d("StackTrace: $stackTrace");
      // Don't rethrow - allow the app to continue with default state
    }
  }

  /// Initialize notification services
  Future<void> _initializeNotifications() async {
    try {
      await NotificationUtil().registerTokenAndSubscribe();
      NotificationUtil().checkPushNotificationInitialMessage();
    } catch (e) {
      DebugHelper.d("Error initializing notifications: $e");
      // Continue without notifications if they fail
    }
  }

  /// Handle navigation arguments safely
  void _handleNavigationArguments() {
    try {
      if (Get.arguments != null && Get.arguments is int) {
        final argumentIndex = Get.arguments as int;
        if (argumentIndex >= 0 && argumentIndex < pageMember.length) {
          index.value = argumentIndex;
        }
      }
    } catch (e) {
      DebugHelper.d("Error handling navigation arguments: $e");
      // Keep default index if arguments are invalid
    }
  }

  /// Handle user authentication and related API calls
  Future<void> _handleUserAuthentication() async {
    try {
      if (userController.isLoggedIn.value) {
        // Make API calls for logged-in users
        await Future.wait([
          userController.callRegisterFcmTokenApi().catchError((e) {
            DebugHelper.d("Error registering FCM token: $e");
          }),
          userController.callGetUnreadNotificationCountApi().catchError((e) {
            DebugHelper.d("Error getting notification count: $e");
          }),
        ]);
      } else {
        // User not logged in - redirect to guest page
        DebugHelper.d("User not logged in, redirecting to guest page");
        AppRouter.router.go(AppRoutePaths.guest);
      }
    } catch (e) {
      DebugHelper.d("Error handling user authentication: $e");
      // Continue with current state if authentication check fails
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    try {
      switch (state) {
        case AppLifecycleState.resumed:
          DebugHelper.d("App resumed");
          await _handleAppResumed();
          break;
        case AppLifecycleState.inactive:
          DebugHelper.d("App inactive");
          break;
        case AppLifecycleState.paused:
          DebugHelper.d("App paused");
          break;
        case AppLifecycleState.detached:
          DebugHelper.d("App detached");
          break;
        case AppLifecycleState.hidden:
          DebugHelper.d("App hidden");
          break;
      }
    } catch (e) {
      DebugHelper.d("Error handling app lifecycle state change: $e");
    }
  }

  /// Handle app resume logic
  Future<void> _handleAppResumed() async {
    try {
      if (userController.isLoggedIn.value) {
        // Small delay to ensure app is fully resumed
        await Future.delayed(const Duration(milliseconds: 500));
        await userController
            .callGetUnreadNotificationCountApi()
            .catchError((e) {
          DebugHelper.d("Error refreshing notification count on resume: $e");
        });
      }
    } catch (e) {
      DebugHelper.d("Error handling app resumed: $e");
    }
  }

  // Lazy initialization of page widgets to avoid creation issues
  List<Widget> get pageMember => [
        HomeView(),
        NewsView(),
        EventsView(),
        LeaderboardView(),
        RewardView(),
        StoreView(),
        AccountView(),
      ];

  /// Change the current page index safely
  void changePage(int newIndex) {
    try {
      if (newIndex >= 0 && newIndex < pageMember.length) {
        if (newIndex != index.value) {
          DebugHelper.d("Changing page from ${index.value} to $newIndex");
          index.value = newIndex;
        }
      } else {
        DebugHelper.d(
            "Invalid page index: $newIndex. Valid range: 0-${pageMember.length - 1}");
      }
    } catch (e) {
      DebugHelper.d("Error changing page: $e");
    }
  }

  /// Get the current page widget safely
  Widget getCurrentPageWidget() {
    try {
      if (index.value >= 0 && index.value < pageMember.length) {
        return pageMember[index.value];
      } else {
        DebugHelper.d("Invalid index ${index.value}, returning default page");
        index.value = 0; // Reset to home page
        return pageMember[0];
      }
    } catch (e) {
      DebugHelper.d("Error getting current page widget: $e");
      // Return a safe fallback widget
      return HomeView();
    }
  }

  /// Navigate to chat list page
  void navigateToChatList() {
    try {
      AppRouter.router.push(AppRoutePaths.chatList);
    } catch (e) {
      DebugHelper.d("Error navigating to chat list: $e");
    }
  }

  /// Get the current page index
  int get currentPageIndex => index.value;

  /// Check if a specific page is currently active
  bool isPageActive(int pageIndex) => index.value == pageIndex;

  /// Legacy method name for backward compatibility
  @Deprecated('Use navigateToChatList() instead')
  void chatPage() => navigateToChatList();
}
