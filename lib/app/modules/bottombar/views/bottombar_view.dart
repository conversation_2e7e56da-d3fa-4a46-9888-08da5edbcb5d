import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/modules/shared/kdrawer/views/kdrawer_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../shared/support_button.dart';
import '../controllers/bottombar_controller.dart';

/// Configuration class for bottom navigation bar items
class _BottomBarConfig {
  static const double iconSize = 30.0;
  static const double verticalPadding = 0.0;
  static const double horizontalPadding = 5.0;
  static const double borderRadius = 25.0;
  static const double elevation = 10.0;

  // Pre-defined border radius for const usage
  static const BorderRadius topBorderRadius = BorderRadius.only(
    topLeft: Radius.circular(borderRadius),
    topRight: Radius.circular(borderRadius),
  );

  /// Navigation item configuration
  static const List<_NavigationItem> navigationItems = [
    _NavigationItem(
      index: 0,
      label: 'Home',
      iconOnPath: 'assets/images/ic_home_on.png',
      iconOffPath: 'assets/images/ic_home_off.png',
    ),
    _NavigationItem(
      index: 1,
      label: 'News',
      iconOnPath: 'assets/images/ic_news_on.png',
      iconOffPath: 'assets/images/ic_news_off.png',
    ),
    _NavigationItem(
      index: 2,
      label: 'Events',
      iconOnPath: 'assets/images/ic_event_on.png',
      iconOffPath: 'assets/images/ic_event_off.png',
    ),
    _NavigationItem(
      index: 3,
      label: 'Leaderboard',
      iconOnPath: 'assets/images/ic_results_on.png',
      iconOffPath: 'assets/images/ic_results_off.png',
    ),
    _NavigationItem(
      index: 4,
      label: 'Reward',
      iconOnPath: 'assets/images/ic_reward_on.png',
      iconOffPath: 'assets/images/ic_reward_off.png',
    ),
    _NavigationItem(
      index: 5,
      label: 'Store',
      iconOnPath: 'assets/images/ic_store_on.png',
      iconOffPath: 'assets/images/ic_store_off.png',
    ),
    _NavigationItem(
      index: 6,
      label: 'Profile',
      iconOnPath: 'assets/images/ic_profile_on.png',
      iconOffPath: 'assets/images/ic_profile_off.png',
    ),
  ];
}

/// Data class for navigation item configuration
class _NavigationItem {
  const _NavigationItem({
    required this.index,
    required this.label,
    required this.iconOnPath,
    required this.iconOffPath,
  });

  final int index;
  final String label;
  final String iconOnPath;
  final String iconOffPath;
}

/// Bottom navigation bar view with improved architecture and performance
class BottomBarView extends StatelessWidget {
  const BottomBarView({super.key});

  // Use GetX dependency injection properly
  BottombarController get controller => Get.find<BottombarController>();
  UserController get userController => Get.find<UserController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      drawer: SizedBox(width: Get.width, child: KDrawerView()),
      body: Obx(() => controller.getCurrentPageWidget()),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: const SupportButton(),
    );
  }

  /// Builds the bottom navigation bar with improved styling and performance
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.transparent,
        borderRadius: _BottomBarConfig.topBorderRadius,
        boxShadow: [
          BoxShadow(
            offset: Offset(0.0, 1.0),
            blurRadius: 4.0,
            color: Colors.black12,
            spreadRadius: 2.0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: _BottomBarConfig.topBorderRadius,
        child: Obx(() => BottomNavigationBar(
              elevation: _BottomBarConfig.elevation,
              backgroundColor: Colors.white,
              type: BottomNavigationBarType.fixed,
              onTap: controller.changePage,
              selectedLabelStyle: const TextStyle(
                color: AppColor.secondaryColor,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: const TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
              ),
              unselectedItemColor: AppColor.primaryColor,
              selectedItemColor: AppColor.secondaryColor,
              currentIndex: controller.index.value,
              showSelectedLabels: false,
              showUnselectedLabels: false,
              iconSize: _BottomBarConfig.iconSize,
              items: _buildNavigationItems(),
            )),
      ),
    );
  }

  /// Builds navigation items using configuration-driven approach
  List<BottomNavigationBarItem> _buildNavigationItems() {
    return _BottomBarConfig.navigationItems
        .map((item) => _buildNavigationItem(item))
        .toList();
  }

  /// Builds a single navigation item with proper styling and state management
  BottomNavigationBarItem _buildNavigationItem(_NavigationItem item) {
    final isSelected = controller.index.value == item.index;

    return BottomNavigationBarItem(
      icon: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: _BottomBarConfig.verticalPadding,
          horizontal: _BottomBarConfig.horizontalPadding,
        ),
        child: Image.asset(
          isSelected ? item.iconOnPath : item.iconOffPath,
          height: _BottomBarConfig.iconSize,
        ),
      ),
      label: item.label,
    );
  }
}
