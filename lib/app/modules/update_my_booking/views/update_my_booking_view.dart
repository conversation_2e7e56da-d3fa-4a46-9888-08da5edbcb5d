import 'package:automoment/app/modules/shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/pit_item/pit_item.dart';
import '../../shared/support_button.dart';
import '../controllers/update_my_booking_controller.dart';

class UpdateMyBookingView extends StatelessWidget {
  UpdateMyBookingView({super.key});

  final UpdateMyBookingController controller =
      Get.put(UpdateMyBookingController());
  final pitOpController = Get.put(PitOpController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Update My Registration',
              style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: VisibilityDetector(
          key: const Key('update_booking_view'),
          onVisibilityChanged: (visibilityInfo) {
            if (visibilityInfo.visibleFraction == 1.0) {
              if (!controller.isVehicleChecked.value) {
                controller.getUserVehiclesAndAvailablePits(
                    showLoadingIndicator: true);
              }
            }
          },
          child: Obx(() {
            return (controller.vehicles.isNotEmpty)
                ? showBookingForm(context)
                : showNoVehicle(context);
          }),
        ));
  }

  showNoVehicle(BuildContext context) {
    if (controller.isVehicleChecked.value) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('You have no vehicle registered',
                style: AppTextStyles.titleText),
            const SizedBox(height: 10),
            const Text('Please register a vehicle to participate in this event',
                style: AppTextStyles.normalText),
            const SizedBox(height: 20),
            ElevatedButton(
                onPressed: () async {
                  controller.isVehicleChecked.value = false;
                  GoRouter.of(context)
                      .push(AppRoutePaths.vehiclesAdd, // Changed to GoRouter
                          extra: 1); // 1 is for booking
                },
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(
                        AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.primaryButtonColor)))),
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  width: 200,
                  child: const Center(
                    child: Text("Register Vehicle",
                        style: TextStyle(fontSize: 16)),
                  ),
                )),
          ],
        ),
      );
    }
    return Container();
  }

  showEventDate(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: Text(
          controller.event.value.title!,
          style: AppTextStyles.titleText,
        ),
      ),
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: Text(
          'Event Date: ${controller.showEventDate()}',
          style: AppTextStyles.normalText,
        ),
      ),
    ]);
  }

  showSelectVehicle(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const Padding(
        padding: EdgeInsets.symmetric(vertical: 5),
        child: Text(
          'Please select your vehicle',
          style: AppTextStyles.normalText,
        ),
      ),
      for (var index = 0; index < controller.vehicles.length; index++)
        GestureDetector(
          onTap: () {
            controller.selectedVehicle.value = controller.vehicles[index].id!;
          },
          child: Container(
            margin: const EdgeInsets.only(top: 10),
            child: Row(
              children: [
                Radio(
                  activeColor: AppColor.primaryColor,
                  value: controller.vehicles[index].id,
                  groupValue: controller.selectedVehicle.value,
                  onChanged: (value) {
                    controller.selectedVehicle.value = value as int;
                    controller.selectedIndex.value = index;
                    //debugPrint(controller.selectedVehicle.value);
                  },
                ),
                // box for vehicle image and name
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColor.primaryColor),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      children: [
                        // vehicle image
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10),
                          child: (controller.vehicles[index].image == "")
                              ? Image.asset(
                                  'assets/images/image_placeholder.png',
                                  width: 150,
                                  height: 60,
                                  fit: BoxFit.fitWidth,
                                )
                              : Image.network(
                                  "${AppConfig.storageUrl}${controller.vehicles[index].image}",
                                  width: 100,
                                  height: 60,
                                  fit: BoxFit.fitWidth,
                                  errorBuilder: (context, error, stackTrace) {
                                  return Image.asset(
                                    'assets/images/image_placeholder.png',
                                    width: 150,
                                    height: 60,
                                    fit: BoxFit.fitWidth,
                                  );
                                }),
                        ),

                        const SizedBox(width: 10),
                        // vehicle name
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${controller.vehicles[index].make!} ${controller.vehicles[index].model!} ${controller.vehicles[index].year ?? ""}",
                                style: AppTextStyles.normalTextBold,
                              ),
                              const SizedBox(height: 5),
                              Text(
                                controller.vehicles[index].type!,
                                style: AppTextStyles.normalText,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
    ]);
  }

  showUpdateButton(BuildContext context) {
    return ElevatedButton(
        onPressed: () async {
          if (controller.selectedVehicle.value == 0) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return const KMessageDialogView(
                  content: "Please select your vehicle",
                );
              },
            );
          } else {
            if (controller.event.value.isRequireVehiclePlateNumber! == true) {
              if (controller.vehicles.isNotEmpty &&
                  controller.selectedIndex.value < controller.vehicles.length) {
                controller.plateNumberController.text = controller
                        .vehicles[controller.selectedIndex.value].plateNumber ??
                    "";
              } else {
                controller.plateNumberController.text = "";
              }

              // controller.plateNumberController.text = controller.vehicles[controller.selectedIndex.value].plateNumber ?? "";

              // show bottom sheet, with rounded corner on top, asking vehicle plate number
              await showModalBottomSheet(
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  context: context,
                  isScrollControlled: true,
                  builder: (context) {
                    return Container(
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(20),
                            child: Text(
                              "Please provide your vehicle's license plate number for this event.",
                              style: AppTextStyles.normalText
                                  .copyWith(height: 1.5),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            width: 150,
                            padding: const EdgeInsets.only(bottom: 20),
                            child: TextFormField(
                              controller: controller.plateNumberController,
                              style: AppTextStyles.normalText.copyWith(
                                fontSize: 20,
                              ),
                              textAlign: TextAlign.center,
                              decoration: const InputDecoration(
                                hintText: "Plate Number",
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 20, right: 20, bottom: 20),
                            child: ElevatedButton(
                                onPressed: () async {
                                  debugPrint(
                                      "onPressed controller.pitOpController.selectedPitNumber.value: ${controller.pitOpController.selectedPitNumber.value}");
                                  if (controller
                                      .plateNumberController.text.isEmpty) {
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return const KMessageDialogView(
                                          content:
                                              "Please enter your vehicle's license plate number",
                                        );
                                      },
                                    );
                                  } else if (controller.pitOpController
                                          .selectedPitNumber.value ==
                                      pitNotSelected) {
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return const KMessageDialogView(
                                          content:
                                              "Please select your preferred pit",
                                        );
                                      },
                                    );
                                  } else {
                                    // submit booking
                                    if (await controller.updateMyBooking()) {
                                      Navigator.of(context).pop();
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context) {
                                          return KMessageDialogView(
                                            content: controller.msg.value,
                                          );
                                        },
                                      );
                                    } else {
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context) {
                                          return KMessageDialogView(
                                            content: controller.msg.value,
                                          );
                                        },
                                      );
                                    }
                                  }
                                },
                                style: ButtonStyle(
                                    foregroundColor:
                                        WidgetStateProperty.all<Color>(
                                            Colors.white),
                                    backgroundColor:
                                        WidgetStateProperty.all<Color>(
                                            AppColor.primaryButtonColor),
                                    shape: WidgetStateProperty.all<
                                            RoundedRectangleBorder>(
                                        RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(18.0),
                                            side: const BorderSide(
                                                color: AppColor
                                                    .primaryButtonColor)))),
                                child: Container(
                                  padding: const EdgeInsets.only(
                                      left: 20, right: 20),
                                  height: 50,
                                  child: const Center(
                                    child: Text("Update My Registration",
                                        style: TextStyle(fontSize: 16)),
                                  ),
                                )),
                          ),
                        ],
                      ),
                    );
                  });
            } else if (controller.pitOpController.selectedPitNumber.value ==
                    pitNotSelected &&
                controller.canChangePit.value == true &&
                controller.pits.isNotEmpty) {
              showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return const KMessageDialogView(
                      content: "Please select your preferred pit",
                    );
                  });
            } else {
              // submit booking
              if (await controller.updateMyBooking()) {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return KMessageDialogView(content: controller.msg.value);
                  },
                );
              } else {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return KMessageDialogView(content: controller.msg.value);
                  },
                );
              }
            }
          }
        },
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side:
                        const BorderSide(color: AppColor.primaryButtonColor)))),
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: const Center(
            child:
                Text("Update My Registration", style: TextStyle(fontSize: 16)),
          ),
        ));
  }

  /////

  showSellMySlotButton(BuildContext context) {
    return ElevatedButton(
        onPressed: () async {
          GoRouter.of(context).push(AppRoutePaths.sellMySlot,
              extra: controller.event.value); // Changed to GoRouter
        },
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.redButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side: const BorderSide(color: AppColor.redButtonColor)))),
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: const Center(
            child: Text("Sell My Slot", style: TextStyle(fontSize: 16)),
          ),
        ));
  }

  showCancelRegistrationButton(BuildContext context) {
    return ElevatedButton(
        onPressed: () async {
          if (controller.cancelButtonEnabled.value == true) {
            showDialog(
                context: Get.context!,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return KConfirmDialogView(
                    content:
                        "Are you sure you want to cancel this registration?",
                    callbackNo: () {
                      Navigator.of(context).pop();
                    },
                    callbackYes: () async {
                      Navigator.of(context).pop();
                      if (await controller.cancelMyBooking()) {
                        await controller.checkCancelMyBookingStatus();
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return KMessageDialogView(
                              content:
                                  "We have received your cancellation request. We will process it as soon as possible.",
                              callback: () {
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      } else {
                        Navigator.of(context).pop();
                        await controller.checkCancelMyBookingStatus();
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return KMessageDialogView(
                              content: controller.msg.value,
                              callback: () {
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      }
                    },
                  );
                });
          }
        },
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor: WidgetStateProperty.all<Color>(
                controller.cancelButtonEnabled.value
                    ? AppColor.redButtonColor
                    : AppColor.redButtonDisableColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side: BorderSide(
                        color: controller.cancelButtonEnabled.value
                            ? AppColor.redButtonColor
                            : AppColor.redButtonDisableColor)))),
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: Center(
            child: Text(controller.cancelButtonTitle.value,
                style: const TextStyle(fontSize: 16)),
          ),
        ));
  }

  showBookingForm(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            showEventDate(context),
            const SizedBox(
              height: 20,
            ),
            showSelectVehicle(context),
            const SizedBox(
              height: 30,
            ),
            (controller.pits.isNotEmpty)
                ? showPitNumberSelection(context)
                : Container(),
            showUpdateButton(context),
            const SizedBox(
              height: 20,
            ),
            showSellMySlotButton(context),
            const SizedBox(
              height: 20,
            ),
            Obx(() {
              return controller.cancelButtonHidden.value
                  ? Container()
                  : showCancelRegistrationButton(context);
            }),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }

  showColorLegend(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      colorLegendItem(Colors.green, "Available"),
      const SizedBox(
        height: 10,
      ),
      colorLegendItem(Colors.grey, "Occupied"),
      const SizedBox(
        height: 10,
      ),
      colorLegendItem(AppColor.redButtonColor, "Current"),
    ]);
  }

  colorLegendItem(Color color, String label) {
    return Row(
      children: [
        Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(7),
          ),
        ),
        const SizedBox(width: 5),
        Text(
          label,
          style: AppTextStyles.normalText,
        ),
      ],
    );
  }

  showPitNumber(BuildContext context) {
    return SizedBox(
      height: 300,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: controller.pits.length,
        itemBuilder: (context, index) {
          final pitController = createPitItemController(
              controller.pits[index].number!,
              controller.pits[index].availability!);
          return PitItem(pitController);
        },
      ),
    );
  }

  PitItemController createPitItemController(
      int pitNumber, List<bool> availability) {
    return PitItemController(
        pitNumber: pitNumber,
        currentPitNumber: pitOpController.selectedPitNumber.value,
        currentPitPosition: pitOpController.selectedPitPosition.value,
        onSelected: (pitNumber, selectedIndex) {
          // Handle the selected pit and car position

          if (controller.canChangePit.value) {
            debugPrint(
                "pitNumber: $pitNumber, carPositionNumber: $selectedIndex");
            pitOpController.selectedPitNumber.value = pitNumber;
            pitOpController.selectedPitPosition.value = selectedIndex;
          } else {
            showDialog(
                context: Get.context!,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return const KMessageDialogView(
                    content: "You cannot change your pit for this event",
                  );
                });
          }
        },
        availability: availability,
        isSelectable: controller.canChangePit.value);
  }

  showPitNumberSelection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: controller.canChangePit.value == true
                ? const Text(
                    'Please select your preferred pit',
                    style: AppTextStyles.normalText,
                  )
                : const Text(
                    'Your pit number',
                    style: AppTextStyles.normalText,
                  )),
        const SizedBox(
          height: 10,
        ),
        showPitNumber(context),
        const SizedBox(
          height: 10,
        ),
        showColorLegend(context),
        const SizedBox(
          height: 30,
        ),
      ],
    );
  }
}
