import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import

import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../../helpers/view_util.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../shared/pit_item/pit_item.dart';
import '../../shared/support_button.dart';
import '../controllers/booking_controller.dart';

class BookingView extends StatelessWidget {
  BookingView({super.key});

  final BookingController controller = Get.put(BookingController());

  final pitOpController = Get.put(PitOpController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: Text(controller.titleBar, style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: VisibilityDetector(
          key: const Key('booking_view'),
          onVisibilityChanged: (visibilityInfo) {
            if (visibilityInfo.visibleFraction == 1.0) {
              if (!controller.isVehicleChecked.value) {
                controller.getUserVehiclesAndAvailablePits(
                    showLoadingIndicator: true);
              }
            }
          },
          child: Obx(() {
            return (controller.vehicles.isNotEmpty)
                ? showBookingForm(context)
                : showNoVehicle(context);
          }),
        ));
  }

  showNoVehicle(BuildContext context) {
    if (controller.isVehicleChecked.value) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('You have no vehicle registered',
                style: AppTextStyles.titleText),
            const SizedBox(height: 10),
            const Text('Please register a vehicle to book an event',
                style: AppTextStyles.normalText),
            const SizedBox(height: 20),
            ElevatedButton(
                onPressed: () async {
                  GoRouter.of(context)
                      .push(AppRoutePaths.vehiclesAdd, // Changed to GoRouter
                          extra: 1); // 1 is for booking
                },
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(
                        AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.primaryButtonColor)))),
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  width: 200,
                  child: const Center(
                    child: Text("Register Vehicle",
                        style: TextStyle(fontSize: 16)),
                  ),
                )),
          ],
        ),
      );
    }
    return Container();
  }

  showBookingForm(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                controller.event.value.title!,
                style: AppTextStyles.titleText,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'Event Date: ${controller.showEventDate()}',
                style: AppTextStyles.normalText,
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            if (controller.type.value == 'additional driver') ...[
              const Text(
                'Your main driver',
                style: AppTextStyles.normalText,
              ),
              ListTile(
                enabled: false,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                leading: CircleAvatar(
                  radius:
                      20, // Adjust this value to change the size of the avatar
                  backgroundColor: Colors
                      .blue, // This will be visible if the image fails to load
                  child: ClipOval(
                    child: Image.network(
                      "${controller.mainDriver.value.photo}",
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // This will be shown if the image fails to load
                        return Text(
                          controller.mainDriver.value.name?.isNotEmpty == true
                              ? controller.mainDriver.value.name![0]
                                  .toUpperCase()
                              : '?',
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        );
                      },
                    ),
                  ),
                ),
                title: Text(
                  controller.mainDriver.value.name ?? '',
                  style: AppTextStyles.normalText
                      .copyWith(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                        controller.mainDriver.value.email != null
                            ? StringUtil.maskEmail(
                                controller.mainDriver.value.email!)
                            : StringUtil.maskMobileNumber(
                                controller.mainDriver.value.mobileNumber!),
                        style: AppTextStyles.normalText.copyWith(fontSize: 12)),
                  ],
                ),
                // trailing: const Icon(Icons.chevron_right, color: Colors.grey),
                onTap: () async {},
              ),
              const SizedBox(
                height: 20,
              ),
            ],
            if (controller.type.value != "additional driver" &&
                controller.type.value != "passenger") ...[
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 5),
                child: Text(
                  'Please select your vehicle',
                  style: AppTextStyles.normalText,
                ),
              ),
              Obx(() {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var index = 0;
                        index < controller.vehicles.length;
                        index++)
                      GestureDetector(
                        onTap: () {
                          controller.selectedVehicle.value =
                              controller.vehicles[index].id!;
                        },
                        child: Container(
                          margin: const EdgeInsets.only(top: 10),
                          child: Row(
                            children: [
                              Radio(
                                activeColor: AppColor.primaryColor,
                                value: controller.vehicles[index].id,
                                groupValue: controller.selectedVehicle.value,
                                onChanged: (value) {
                                  controller.selectedVehicle.value =
                                      value as int;
                                  controller.selectedIndex.value = index;
                                },
                              ),
                              // box for vehicle image and name
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColor.primaryColor),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    children: [
                                      // vehicle image
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: (controller
                                                    .vehicles[index].image ==
                                                "")
                                            ? Image.asset(
                                                "assets/images/placeholder_vehicle.jpg",
                                                width: 150,
                                                height: 60,
                                                fit: BoxFit.fitWidth,
                                              )
                                            : Image.network(
                                                "${AppConfig.storageUrl}${controller.vehicles[index].image}",
                                                width: 100,
                                                height: 60,
                                                fit: BoxFit.fitWidth,
                                                errorBuilder: (context, error,
                                                    stackTrace) {
                                                return Image.asset(
                                                  "assets/images/placeholder_vehicle.jpg",
                                                  width: 150,
                                                  height: 60,
                                                  fit: BoxFit.fitWidth,
                                                );
                                              }),
                                      ),

                                      const SizedBox(width: 10),
                                      // vehicle name
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "${controller.vehicles[index].make!} ${controller.vehicles[index].model!} ${controller.vehicles[index].year ?? ""}",
                                              style:
                                                  AppTextStyles.normalTextBold,
                                            ),
                                            const SizedBox(height: 5),
                                            Text(
                                              controller.vehicles[index].type!,
                                              style: AppTextStyles.normalText,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                );
              }),
            ],
            if (controller.type.value != "additional driver" &&
                controller.type.value != "passenger" &&
                controller.pits.isNotEmpty)
              showPitNumberSelection(context),
            if (controller.type.value == "group") ...[
              const SizedBox(
                height: 40,
              ),
              const Padding(
                padding: EdgeInsets.only(bottom: 20),
                child: Text(
                  'Your group participants',
                  style: AppTextStyles.normalText,
                ),
              ),
              showParticipantList(context),
            ],
            const SizedBox(
              height: 30,
            ),
            ElevatedButton(
                onPressed: () async {
                  if (controller.type.value == "additional driver" ||
                      controller.type.value == "passenger") {
                    await controller.submitBooking(controller, context);
                  } else if (controller.selectedVehicle.value == 0) {
                    ViewUtil.showErrorDialog(
                        context, "Please select your vehicle");
                  } else if (pitOpController.selectedPitPosition.value ==
                          pitNotSelected &&
                      controller.pits.isNotEmpty) {
                    ViewUtil.showErrorDialog(context, "Please select your pit");
                  } else if (controller
                          .event.value.isRequireVehiclePlateNumber ==
                      true) {
                    await controller.showPlateNumberBottomSheet(
                        context, controller);
                  } else {
                    await controller.submitBooking(controller, context);
                  }
                },
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(
                        AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.primaryButtonColor)))),
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  child: const Center(
                    child: Text("Complete Registration",
                        style: TextStyle(fontSize: 16)),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  showParticipantList(BuildContext context) {
    return Obx(
      () {
        return Column(
          children: controller.participants.map((user) {
            return Container(
              decoration: BoxDecoration(
                color: controller.participants.contains(user)
                    ? Colors.blue.withAlpha(26)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: 8.0), // Add some spacing between rows
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.blue,
                      child: ClipOval(
                        child: Image.network(
                          "${user.userPhoto}",
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Text(
                              user.name?.isNotEmpty == true
                                  ? user.name![0].toUpperCase()
                                  : '?',
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                        width:
                            16), // Add some spacing between the avatar and text
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user.name ?? '',
                            style: AppTextStyles.normalText
                                .copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(user.vehicle ?? '',
                              style: AppTextStyles.normalText
                                  .copyWith(fontSize: 12)),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  showColorLegend(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      colorLegendItem(Colors.green, "Available"),
      const SizedBox(
        height: 10,
      ),
      colorLegendItem(Colors.grey, "Occupied"),
      const SizedBox(
        height: 10,
      ),
      colorLegendItem(AppColor.redButtonColor, "Current"),
    ]);
  }

  colorLegendItem(Color color, String label) {
    return Row(
      children: [
        Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(7),
          ),
        ),
        const SizedBox(width: 5),
        Text(
          label,
          style: AppTextStyles.normalText,
        ),
      ],
    );
  }

  showPitNumberSelection(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const SizedBox(
        height: 30,
      ),
      const Padding(
        padding: EdgeInsets.symmetric(vertical: 5),
        child: Text(
          'Please select your preferred pit',
          style: AppTextStyles.normalText,
        ),
      ),
      const SizedBox(
        height: 10,
      ),
      showPitNumber(context),
      const SizedBox(
        height: 10,
      ),
      showColorLegend(context),
    ]);
  }

  showPitNumber(BuildContext context) {
    return SizedBox(
      height: 300,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: controller.pits.length,
        itemBuilder: (context, index) {
          final pitController = createPitItemController(
              controller.pits[index].number!,
              controller.pits[index].availability!);
          return PitItem(pitController);
        },
      ),
    );
  }

  PitItemController createPitItemController(
      int pitNumber, List<bool> availability) {
    return PitItemController(
        pitNumber: pitNumber,
        onSelected: (pitNumber, selectedIndex) {
          // Handle the selected pit and car position
          debugPrint(
              "pitNumber: $pitNumber, carPositionNumber: $selectedIndex");
          pitOpController.selectedPitNumber.value = pitNumber;
          pitOpController.selectedPitPosition.value = selectedIndex;
        },
        availability: availability,
        isSelectable: true);
  }

  //
}
