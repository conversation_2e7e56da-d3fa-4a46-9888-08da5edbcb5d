import 'dart:convert';

import 'package:automoment/app/modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:go_router/go_router.dart'; // Added GoRouter import
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:intl/intl.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/driver_lookup_model.dart';
import '../../../models/event_modal.dart';
import '../../../models/participant_model.dart';
import '../../../models/pit_model.dart';
import '../../../models/vehicle_model.dart';
import '../../../routes/app_router.dart'; // Changed to app_router.dart
import '../../../services/api_client.dart';
import '../../events_detail/controllers/events_detail_controller.dart';
import '../../shared/pit_item/pit_item.dart';
import '../views/plate_number_bottom_sheet.dart';

class BookingController extends BaseScreenController {
  var event = Event().obs;
  String msg = "";
  var isLoading = false.obs;

  var vehicles = <Vehicle>[].obs;
  var selectedVehicle = 0.obs;
  var selectedIndex = 0.obs;

  var pits = <Pit>[].obs;

  TextEditingController plateNumberController = TextEditingController();

  final UserController userController = Get.put(UserController());

  var isVehicleChecked = false.obs;

  PitOpController pitOpController = Get.put(PitOpController());

  var type = "driver".obs;
  var mainDriver = MainDriver().obs;

  var titleBar = "Driver Registration";
  var participants = <Participant>[].obs;

  var groupId = 0.obs;

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;

    if (data.runtimeType == Event) {
      // check it because it is called from add vehicle too
      event.value = data;
    }

    //

    if (Get.arguments is Event) {
      event.value = Get.arguments;
    } else {
      event.value = Get.arguments['event'];
      type.value = Get.arguments['type'];
    }

    if (type.value == "additional driver") {
      mainDriver.value = Get.arguments['mainDriver'];
      titleBar = "Additional Driver Registration";
    }

    if (type.value == "passenger") {
      titleBar = "Passenger Registration";
    }

    if (type.value == "group") {
      titleBar = "Group Registration";

      participants.value = Get.arguments['participant'];
    }

    //getVehicles(showLoadingIndicator: true);
  }

  @override
  String get screenName => 'Booking';

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<bool> bookEvent() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      // Use the optional parameters for plateNumber, pitNumber, and pitPosition
      String response = await ApiClient.registerEvent(
          userController.getToken(),
          userController.user.value.id!,
          event.value.id!,
          selectedVehicle.value,
          plateNumberController.text, // This will be null if not required
          (pitOpController.selectedPitNumber.value == pitNotSelected)
              ? null
              : pitOpController
                  .selectedPitNumber.value, // This will be null if not provided
          (pitOpController.selectedPitPosition.value == pitNotSelected)
              ? null
              : pitOpController.selectedPitPosition
                  .value // This will be null if not provided
          );

      msg = jsonDecode(response)['message'];
      isLoading.value = false;
      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'];

      if (success) {
        // Log booking started event
        await analytics.logBookingStarted(
          eventId: event.value.id.toString(),
          eventName: event.value.title ?? '',
        );
      }

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }

  Future<void> getVehicles({bool showLoadingIndicator = false}) async {
    try {
      if (showLoadingIndicator) {
        EasyLoading.show(status: 'Loading...');
      }

      var response = await ApiClient.getVehicles(
          userController.getToken(), userController.user.value.id!);
      var list = jsonDecode(response)['data'];
      var l = <Vehicle>[];

      list.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l.add(n);
      });

      vehicles.value = l;

      isVehicleChecked.value = true;

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> getUserVehiclesAndAvailablePits(
      {bool showLoadingIndicator = false}) async {
    try {
      if (showLoadingIndicator) {
        EasyLoading.show(status: 'Loading...');
      }

      var response = await ApiClient.getUserVehiclesAndAvailablePits(
          userController.getToken(), event.value.id!);

      // --
      var list = jsonDecode(response)['vehicles'];
      var l = <Vehicle>[];

      list.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l.add(n);
      });

      vehicles.value = l;

      // --
      var list2 = jsonDecode(response)['pits'];
      var l2 = <Pit>[];

      list2.forEach((dynamic d) {
        var n = Pit.fromJson(d);
        l2.add(n);
      });

      pits.value = l2;

      //

      isVehicleChecked.value = true;

      //

      // group need the vehicle selected by default
      if (type.value == "group") {
        var vehicleName = participants.first.vehicle;

        debugPrint('vehicleName: $vehicleName');

        if (vehicleName != null) {
          for (var i = 0; i < vehicles.length; i++) {
            String v = "${vehicles[i].make!} ${vehicles[i].model!}";

            if (vehicles[i].year != null) {
              v = "$v ${vehicles[i].year}";
            }

            if (v == vehicleName) {
              selectedVehicle.value = vehicles[i].id!;
              break;
            }
          }
        }
      }

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> registerAsAdditionalDriver() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.registerAsAdditionalDriver(
          userController.getToken(),
          event.value.id!,
          mainDriver.value.id!,
          selectedVehicle.value,
          plateNumberController.text, // This will be null if not required
          (pitOpController.selectedPitNumber.value == pitNotSelected)
              ? null
              : pitOpController
                  .selectedPitNumber.value, // This will be null if not provided
          (pitOpController.selectedPitPosition.value == pitNotSelected)
              ? null
              : pitOpController.selectedPitPosition
                  .value // This will be null if not provided
          );
      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();
      return jsonDecode(response)['success'] ?? false;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> registerAsPassenger() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.registerAsPassenger(
          userController.getToken(), event.value.id!);
      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();
      return jsonDecode(response)['success'] ?? false;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> registerAsGroup() async {
    try {
      String participantsJson =
          jsonEncode(participants.map((e) => e.toJson()).toList());

      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.registerAsGroup(
          userController.getToken(),
          event.value.id!,
          participantsJson,
          selectedVehicle.value,
          plateNumberController.text, // This will be null if not required
          (pitOpController.selectedPitNumber.value == pitNotSelected)
              ? null
              : pitOpController
                  .selectedPitNumber.value, // This will be null if not provided
          (pitOpController.selectedPitPosition.value == pitNotSelected)
              ? null
              : pitOpController.selectedPitPosition
                  .value // This will be null if not provided
          );
      msg = jsonDecode(response)['message'];
      bool success = jsonDecode(response)['success'] ?? false;

      if (success) {
        groupId.value = jsonDecode(response)['group_id'];
      }

      EasyLoading.dismiss();
      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> submitBooking(dynamic controller, BuildContext context) async {
    bool success;

    switch (controller.type.value) {
      case 'additional driver':
        success = await controller.registerAsAdditionalDriver();
        break;
      case 'passenger':
        success = await controller.registerAsPassenger();
        break;
      case 'group':
        success = await controller.registerAsGroup();
        break;
      default:
        success = await controller.bookEvent();
    }

    if (success) {
      Get.find<EventsDetailController>().fetchCheckBookingStatus();

      // Log booking completed event
      await analytics.logCustomEvent(
        name: 'booking_completed',
        parameters: {
          'event_id': controller.event.value.id.toString(),
          'event_name': controller.event.value.title ?? '',
          'booking_type': controller.type.value,
          'participants_count': controller.participants.length ?? 1,
        },
      );

      final String routePath = controller.type.value == 'passenger'
          ? AppRoutePaths.eventsForm // Use new constant
          : AppRoutePaths.makePayment; // Use new constant

      // GoRouter equivalent for Get.offNamedUntil.
      // .go() clears the stack and navigates.
      // If a specific "until" behavior is strictly needed,
      // it might require more complex logic with ShellRoutes or custom navigator observers.
      // For now, a simple .go() to the target route.
      // The context for GoRouter.of(context) needs to be available.
      // If this controller method is called from a view, context should be passed.
      // Assuming Get.context is available and valid here for now.
      if (Get.context != null) {
        GoRouter.of(Get.context!).go(routePath, extra: {
          "event": controller.event.value,
          "type": controller.type.value.toLowerCase(),
          "qty": controller.participants.length > 0
              ? controller.participants.length
              : 1, // Ensure qty is at least 1
          "groupId": controller.groupId.value > 0
              ? controller.groupId.value
              : 0 // Ensure groupId is valid
        });
      } else {
        // Fallback or error handling if context is not available
        debugPrint(
            "Error: Get.context is null, cannot navigate with GoRouter.");
        // Potentially show a global snackbar or log error
      }
    } else {
      debugPrint("show error dialog: ${controller.msg}");
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return KMessageDialogView(content: controller.msg);
        },
      );
    }
  }

  Future<void> showPlateNumberBottomSheet(
      BuildContext context, dynamic controller) async {
    controller.plateNumberController.text =
        controller.vehicles[controller.selectedIndex.value].plateNumber ?? "";

    await showModalBottomSheet(
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      context: context,
      isScrollControlled: true,
      builder: (context) => PlateNumberBottomSheet(controller: controller),
    );
  }
}
