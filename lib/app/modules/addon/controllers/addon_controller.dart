import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../constants/app_config.dart';
import '../../../models/addon_model.dart';
import '../../../models/event_modal.dart';
import '../../make_payment/controllers/make_payment_controller.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';

class AddonController extends BaseScreenController {
  @override
  String get screenName => 'Addon';

  var event = Event();
  var addonList = [].obs;
  var purchasedAddonList = [].obs;
  var addonListCheckbox = [].obs;
  var currency = "SGD".obs;
  var currencyPrefix = "SGD".obs;
  var totalAmount = 0.0.obs;

  var payNowId = 0.obs;
  UniqueKey webviewKey = UniqueKey();
  final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory(() => EagerGestureRecognizer())
  };
  late WebViewController webViewController;

  late CreatePaymentIntentResult paymentIntentData;
  var isPaymentSuccess = false.obs;

  final UserController userController = Get.put(UserController());

  @override
  Future<void> onInit() async {
    super.onInit();
    event = Get.arguments;
    await fetchAddon();
    Stripe.publishableKey = AppConfig.stripePublishableKey;
    await Stripe.instance.applySettings();
  }

  Future<void> fetchAddon() async {
    EasyLoading.show(status: 'Loading...');
    addonListCheckbox.clear();
    addonList.clear();
    purchasedAddonList.clear();

    try {
      var response =
          await ApiClient.getAddons(userController.getToken(), event.id!);
      addonList.value =
          jsonDecode(response)['addons'].map((e) => Addon.fromJson(e)).toList();
      addonList.map((element) => addonListCheckbox.add(false)).toList();

      purchasedAddonList.value = jsonDecode(response)['purchased']
          .map((e) => Addon.fromJson(e))
          .toList();

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
    }
  }

  void switchCurrency() {
    if (currency.value == 'SGD') {
      currency.value = 'MYR';
      currencyPrefix.value = 'RM';
      calculateTotalAmount();
    } else {
      currency.value = 'SGD';
      currencyPrefix.value = 'SGD';
      calculateTotalAmount();
    }
  }

  void calculateTotalAmount() {
    totalAmount.value = 0.0;
    for (int i = 0; i < addonListCheckbox.length; i++) {
      if (addonListCheckbox[i]) {
        totalAmount.value += (currency.value == 'SGD')
            ? addonList[i].price
            : addonList[i].priceMyr;
      }
    }
  }

  Future<bool> createPaymentIntent() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.orderAddon(userController.getToken(),
          event.id!, getSelectedAddon(), currency.value, "Stripe");

      paymentIntentData = CreatePaymentIntentResult(
        clientSecret: jsonDecode(response)['client_secret'],
        ephemeralKey: jsonDecode(response)['ephemeral_key'],
        customerId: jsonDecode(response)['customer_id'],
      );

      EasyLoading.dismiss();
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> makePayment() async {
    if (getSelectedAddon().isEmpty) {
      BuildContext context = Get.context!;
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return KMessageDialogView(
                content: "Please select at least 1 addon",
                callback: () {
                  Navigator.of(context).pop();
                });
          });
    } else {
      try {
        await createPaymentIntent();

        await Stripe.instance.initPaymentSheet(
            paymentSheetParameters: SetupPaymentSheetParameters(
          merchantDisplayName: 'Automoment',
          customerId: paymentIntentData.customerId,
          paymentIntentClientSecret: paymentIntentData.clientSecret,
          customerEphemeralKeySecret: paymentIntentData.ephemeralKey,
          style: ThemeMode.light,
          allowsDelayedPaymentMethods: false,
          returnURL: 'automoment://payment', // Add return URL scheme
          billingDetails: const BillingDetails(
            address: Address(
              city: 'Singapore',
              country: 'SG',
              line1: '',
              line2: '',
              state: 'Singapore',
              postalCode: '',
            ),
          ),
        ));

        displayPaymentSheet();
      } catch (e, s) {
        debugPrint('exception:$e$s');
      }
    }
  }

  displayPaymentSheet() async {
    try {
      await Stripe.instance.presentPaymentSheet();
      //isPaymentSuccess.value = true;

      // Get.snackbar('Payment', 'Payment Successful',
      //     snackPosition: SnackPosition.BOTTOM,
      //     backgroundColor: Colors.green,
      //     colorText: Colors.white,
      //     margin: const EdgeInsets.all(10),
      //     duration: const Duration(seconds: 2));

      showThankYouMessage();
      await fetchAddon();
    } on Exception catch (e) {
      if (e is StripeException) {
        debugPrint("Error from Stripe: ${e.error.localizedMessage}");
      } else {
        debugPrint("Unforeseen error: $e");
      }
    } catch (e) {
      debugPrint("exception:$e");
    }
  }

  List<int> getSelectedAddon() {
    List<int> selectedAddon = [];
    for (int i = 0; i < addonListCheckbox.length; i++) {
      if (addonListCheckbox[i]) {
        selectedAddon.add(addonList[i].id);
      }
    }
    return selectedAddon;
  }

  void showThankYouMessage() {
    BuildContext context = Get.context!;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return KMessageDialogView(
              content: "Payment Successful. Thank you for your purchase.",
              callback: () async {
                await fetchAddon();
                Navigator.of(context).pop();
              });
        });
  }

  Future<void> makePaymentPayNow() async {
    debugPrint("makePaymentPayNow");

    if (getSelectedAddon().isEmpty) {
      BuildContext context = Get.context!;
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return KMessageDialogView(
                content: "Please select at least 1 addon",
                callback: () {
                  Navigator.of(context).pop();
                });
          });
    } else {
      try {
        payNowId.value = await createPaymentIntentPayNow();

        // get paynow id
      } catch (e, s) {
        debugPrint('exception:$e$s');
      }
    }

    // try {
    //   payNowId.value = await createPaymentIntentPayNow();
    //
    //   debugPrint("paymentId: ${payNowId.value}");
    //
    //   if (payNowId.value != null) {
    //     // open webview paynow
    //   }
    // } catch (e, s) {
    //   debugPrint('exception:$e$s');
    // }
  }

  // Future<int> createPaymentIntentPayNow() async {
  //   EasyLoading.show(status: 'Please wait...');
  //   try {
  //     var response = await ApiClient.payNowPayment(
  //         userController.getToken(),
  //         event.value.id!,
  //         couponCodeController.text,
  //         isAdditionalDriver.value
  //     );
  //
  //     isLoading.value = false;
  //     EasyLoading.dismiss();
  //     return jsonDecode(response)['payment_id'];
  //   } catch(e) {
  //     EasyLoading.dismiss();
  //     isLoading.value = false;
  //     throw Exception(e.toString());
  //   }
  // }

  Future<int> createPaymentIntentPayNow() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.orderAddon(userController.getToken(),
          event.id!, getSelectedAddon(), currency.value, "PayNow");

      debugPrint(response);

      // paymentIntentData = CreatePaymentIntentResult(
      //   clientSecret: jsonDecode(response)['client_secret'],
      //   ephemeralKey: jsonDecode(response)['ephemeral_key'],
      //   customerId: jsonDecode(response)['customer_id'],
      // );

      EasyLoading.dismiss();
      return jsonDecode(response)['payment_id'];
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
