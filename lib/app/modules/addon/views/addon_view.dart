import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../../models/addon_model.dart';
import '../../shared/support_button.dart';
import '../controllers/addon_controller.dart';

class AddonView extends GetView<AddonController> {
  const AddonView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Addon Purchase', style: AppTextStyles.titleText),
          elevation: 1,
          actions: [
            IconButton(
                onPressed: () {
                  controller.switchCurrency();
                },
                icon: Image.asset(
                  'assets/images/sgdmyr.png',
                  width: 30,
                ))
          ],
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(() {
          return SingleChildScrollView(
              child: Padding(
            padding: const EdgeInsets.all(20.0),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              (controller.purchasedAddonList.isNotEmpty)
                  ? purchasedAddonView()
                  : const SizedBox(),
              (controller.addonList.isNotEmpty)
                  ? availableAddonView(context)
                  : const SizedBox(),
            ]),
          ));
        }));
  }

  Widget purchasedAddonView() {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        (controller.purchasedAddonList.length > 1)
            ? 'Purchased Addons'
            : 'Purchased Addon',
        style: AppTextStyles.normalTextBold.copyWith(
          color: AppColor.primaryButtonColor,
          fontSize: 18,
        ),
      ),
      const SizedBox(height: 20),
      ...List.generate(controller.purchasedAddonList.length, (index) {
        return purchasedAddonCheckList(
            controller.purchasedAddonList[index], index);
      }),
    ]);
  }

  Widget availableAddonView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Text(
          (controller.addonList.length > 1)
              ? 'Available Addons'
              : 'Available Addon',
          style: AppTextStyles.normalTextBold.copyWith(
            color: AppColor.primaryButtonColor,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 20),
        ...List.generate(controller.addonList.length, (index) {
          return addonCheckList(controller.addonList[index], index);
        }),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Total amount',
              style: AppTextStyles.normalTextBold.copyWith(
                color: AppColor.primaryButtonColor,
                fontSize: 18,
              ),
            ),
            Text(
              '${controller.currencyPrefix} ${StringUtil.formatMoneyWithoutCent(controller.totalAmount.value)}',
              style: AppTextStyles.normalTextBold.copyWith(
                color: AppColor.primaryButtonColor,
                fontSize: 25,
              ),
            ),
          ],
        ),
        const SizedBox(height: 40),
        ElevatedButton(
            onPressed: () async {
              await controller.makePayment();
            },
            style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                backgroundColor:
                    WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.0),
                        side: const BorderSide(
                            color: AppColor.primaryButtonColor)))),
            child: Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              height: 50,
              child: const Center(
                child: Text("Pay", style: TextStyle(fontSize: 16)),
              ),
            )),
        const SizedBox(
          height: 20,
        ),
        // (controller.currency.value == "SGD")
        //     ? ElevatedButton(
        //         onPressed: () async {
        //           await controller.makePaymentPayNow();

        //           print("paynowId: ${controller.payNowId.value}");

        //           if (controller.getSelectedAddon().isNotEmpty) {
        //             showModalBottomSheet(
        //                 isScrollControlled: true,
        //                 shape: RoundedRectangleBorder(
        //                   borderRadius: BorderRadius.circular(20.0),
        //                 ),
        //                 backgroundColor: Colors.white,
        //                 builder: (context) {
        //                   return (controller.payNowId.value == 0)
        //                       ? Container()
        //                       : Stack(children: [
        //                           WebView(
        //                             key: controller.webviewKey,
        //                             backgroundColor: Colors.white,
        //                             initialUrl:
        //                                 "${AppConfig.webUrl}paynow/${controller.payNowId.value}?${StringUtil.randomString(10)}",
        //                             javascriptMode: JavascriptMode.unrestricted,

        //                             gestureNavigationEnabled: true,
        //                             gestureRecognizers:
        //                                 controller.gestureRecognizers,

        //                             //

        //                             navigationDelegate:
        //                                 (NavigationRequest request) {
        //                               print(
        //                                   "navigationDelegate: ${request.url}");

        //                               if (Platform.isAndroid) {
        //                                 if (request.url.contains(
        //                                         "https://qr.stripe.com") &&
        //                                     request.url
        //                                         .contains("download=true")) {
        //                                   GallerySaver.saveImage(request.url)
        //                                       .then((bool? success) {
        //                                     showDialog(
        //                                         context: context,
        //                                         barrierDismissible: false,
        //                                         builder:
        //                                             (BuildContext context) {
        //                                           return KMessageDialogView(
        //                                               content:
        //                                                   "The QR code has been saved to your gallery. You can also access it with File Manager > Pictures.",
        //                                               callback: () {
        //                                                 // Navigator.of(context).pop();
        //                                                 Navigator.of(context).pop(); // Changed from Get.back()
        //                                               });
        //                                         });
        //                                   });
        //                                   return NavigationDecision.prevent;
        //                                 }
        //                               }

        //                               return NavigationDecision.navigate;
        //                             },

        //                             //

        //                             onWebViewCreated: (webViewController) {
        //                               controller.webViewController =
        //                                   webViewController;
        //                             },

        //                             onProgress: (value) {},

        //                             onPageStarted: (url) async {
        //                               print("onPageStarted: $url");
        //                               EasyLoading.show(
        //                                   status: 'Please wait...');
        //                             },

        //                             onPageFinished: (url) async {
        //                               print("onPageFinished: $url");

        //                               if (url ==
        //                                   "${AppConfig.webUrl}paynow/return/success") {
        //                                 // delay 5 second
        //                                 //EasyLoading.show(status: 'Please wait...');
        //                                 await Future.delayed(
        //                                     const Duration(seconds: 2),
        //                                     () async {
        //                                   print('delay 2 second');

        //                                   Navigator.of(context).pop();

        //                                   // action after successful payment
        //                                   controller.showThankYouMessage();
        //                                   //await controller.fetchAddon();
        //                                 });
        //                                 //await controller.checkBookingStatus();
        //                                 EasyLoading.dismiss();
        //                               } else {
        //                                 EasyLoading.dismiss();
        //                               }
        //                             },
        //                           ),
        //                           Align(
        //                             alignment: Alignment.topRight,
        //                             child: Container(
        //                               margin: const EdgeInsets.only(
        //                                   top: 50.0, right: 20.0),
        //                               child: IconButton(
        //                                 onPressed: () {
        //                                   Navigator.of(context).pop();
        //                                 },
        //                                 icon: const Icon(Icons.close),
        //                                 iconSize: 30,
        //                                 color: Colors.black,
        //                               ),
        //                             ),
        //                           ),
        //                           Align(
        //                             alignment: Alignment.topRight,
        //                             child: Container(
        //                               margin: const EdgeInsets.only(
        //                                   top: 12, right: 12),
        //                               child: // white box rectangle
        //                                   Container(
        //                                 width: 25,
        //                                 height: 25,
        //                                 decoration: const BoxDecoration(
        //                                   color: Colors.white,
        //                                 ),
        //                               ),
        //                             ),
        //                           ),
        //                         ]); //payNowView(controller.payNowId.value);
        //                 },
        //                 context: context);
        //           }
        //         },
        //         style: ButtonStyle(
        //             foregroundColor:
        //                 MaterialStateProperty.all<Color>(Colors.white),
        //             backgroundColor: MaterialStateProperty.all<Color>(
        //                 AppColor.primaryButtonColor),
        //             shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //                 RoundedRectangleBorder(
        //                     borderRadius: BorderRadius.circular(18.0),
        //                     side: const BorderSide(
        //                         color: AppColor.primaryButtonColor)))),
        //         child: Container(
        //           padding: const EdgeInsets.only(left: 20, right: 20),
        //           height: 50,
        //           child: const Center(
        //             child:
        //                 Text("Pay with PayNow", style: TextStyle(fontSize: 16)),
        //           ),
        //         ))
        //     : Container(),
      ],
    );
  }

  Widget purchasedAddonCheckList(Addon addon, index) {
    return Column(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: AppColor.kLightBgColor,
          ),
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              addon.name!,
              style: AppTextStyles.normalText,
            ),
          ),
        ),
        const SizedBox(
          height: 10,
        )
      ],
    );
  }

  Widget addonCheckList(Addon addon, index) {
    return Column(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: AppColor.kLightBgColor,
          ),
          child: CheckboxListTile(
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    addon.name!,
                    style: AppTextStyles.normalText,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                //const Spacer(),
                (controller.currency.value == 'SGD')
                    ? Text(
                        "${controller.currencyPrefix.value} ${addon.price}",
                      )
                    : Text(
                        "${controller.currencyPrefix.value} ${addon.priceMyr}",
                      )
              ],
            ),
            value: controller.addonListCheckbox[index] ?? false,
            onChanged: (newValue) {
              controller.addonListCheckbox[index] = newValue;
              controller.calculateTotalAmount();
            },
            controlAffinity:
                ListTileControlAffinity.leading, //  <-- leading Checkbox
          ),
        ),
        const SizedBox(
          height: 10,
        )
      ],
    );
  }
}
