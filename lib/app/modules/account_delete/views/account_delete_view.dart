import 'package:automoment/app/constants/app_config.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/account_delete_controller.dart';

class AccountDeleteView extends GetView<AccountDeleteController> {
  const AccountDeleteView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Delete My Account', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: ListView(
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 20),
              child: Text(
                'Are you sure you want to delete your account?',
                style: AppTextStyles.normalText,
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'WARNING!',
                style: AppTextStyles.normalTextBold,
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'This action is not reversible. You will lose access to Automoment app.',
                style: AppTextStyles.normalText,
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            ElevatedButton(
                style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor:
                        WidgetStateProperty.all<Color>(AppColor.redButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(
                                color: AppColor.redButtonColor)))),
                onPressed: () async {
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return KConfirmDialogView(
                            content:
                                "Are you sure want to delete your account?",
                            callbackYes: () async {
                              // Close the confirmation dialog first
                              Navigator.of(context).pop();

                              // Execute the API call
                              final success =
                                  await controller.callUserDeleteRequestApi();

                              if (success) {
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (BuildContext context) {
                                    return KMessageDialogView(
                                      content: controller.msg,
                                      callback: () {
                                        // Navigate back multiple times
                                        Navigator.of(context).pop();
                                        Navigator.of(context).pop();
                                        Navigator.of(context).pop();
                                      },
                                    );
                                  },
                                );
                              } else {
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (BuildContext context) {
                                    return KMessageDialogView(
                                      content:
                                          "We have problem connecting to server, please try again later.",
                                      callback: () {
                                        Navigator.of(context).pop();
                                      },
                                    );
                                  },
                                );
                              }
                            },
                            callbackNo: () {
                              Navigator.of(context).pop();
                            });
                      });
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  child: const Center(
                    child: Text("Yes, delete my account",
                        style: TextStyle(fontSize: 16)),
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
