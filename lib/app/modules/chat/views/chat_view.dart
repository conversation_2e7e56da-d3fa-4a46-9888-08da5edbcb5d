import 'package:flutter/material.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../shared/support_button.dart';
import '../controllers/chat_controller.dart';

class ChatView extends StatelessWidget {
  ChatView({super.key}) {
    // Initialize controller using Get.find() as per project pattern
    controller = Get.find<ChatController>();
  }

  // Using late final for controller as per project pattern
  late final ChatController controller;
  // Context will be provided in build method, no need to store as field

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: Row(children: [
            CircleAvatar(
              backgroundImage: NetworkImage(
                  "${AppConfig.storageUrl}/${controller.chatImage}"),
            ),
            const SizedBox(width: 10),
            Text(
              controller.chatTitle,
              style: const TextStyle(
                  color: AppColor.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16),
            )
          ]),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(() {
          return Chat(
            messages: controller.messages,
            onSendPressed: controller.handleSendPressed,
            user: controller.user,
            showUserAvatars: true,
            showUserNames: true,
            onAttachmentPressed: () => showAttachmentBottomSheet(context),
            onPreviewDataFetched: controller.handlePreviewDataFetched,
            onMessageTap: controller.handleMessageTap,
            theme: const DefaultChatTheme(
              primaryColor: AppColor.secondaryColor,
            ),
          );
        }));
  }

  void showAttachmentBottomSheet(BuildContext context) {
    showModalBottomSheet<void>(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return Container(
              height: 230,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30))),
              child: Column(children: [
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Attachment",
                  style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColor.primaryColor),
                ),
                const SizedBox(
                  height: 20,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        controller.handleImageSelection();
                        Navigator.of(context).pop();
                      },
                      child: const Text(
                        "Photo From Gallery",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        controller.handleCameraSelection();
                        Navigator.of(context).pop();
                      },
                      child: const Text(
                        "Take a Photo",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        controller.handleFileSelection();
                        Navigator.of(context).pop();
                      },
                      child: const Text(
                        "Attach File",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ]));
        });
  }
}
