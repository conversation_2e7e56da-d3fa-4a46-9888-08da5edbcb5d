import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';
import '../../booking/controllers/booking_controller.dart';
import '../../vehicles/controllers/vehicles_controller.dart';

class VehiclesAddController extends BaseScreenController {
  @override
  String get screenName => 'Add Vehicle';

  TextEditingController makeController = TextEditingController();
  TextEditingController modelController = TextEditingController();
  TextEditingController yearController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  var msg = "";

  var selectedType = "".obs;
  var image64 = "".obs;

  final UserController userController = Get.put(UserController());
  final VehiclesController vehiclesController = Get.put(VehiclesController());

  final ImagePicker _picker = ImagePicker();

  bool isFromBooking = false;

  var typeItems = ['SG Registered', 'MY Registered', 'Track Car'];

  @override
  void onInit() {
    super.onInit();
    var data = Get.arguments;

    if (data == 1) {
      isFromBooking = true;
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<bool> addVehicle() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.addVehicles(
          userController.getToken(),
          userController.user.value.id!,
          makeController.text,
          modelController.text,
          yearController.text,
          selectedType.value,
          image64.value);
      EasyLoading.dismiss();
      var success = (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      if (success) {
        vehiclesController.getVehicles();
      }

      if (isFromBooking) {
        final BookingController bookingController =
            Get.put(BookingController());
        bookingController.getVehicles(showLoadingIndicator: false);
      }

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> getImageFromGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    final imageBytes = await image?.readAsBytes();
    image64.value = base64.encode(imageBytes!);
    Navigator.of(Get.context!).pop();
  }

  Future<void> getImageFromCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    final imageBytes = await image?.readAsBytes();
    image64.value = base64.encode(imageBytes!);
    Navigator.of(Get.context!).pop();
  }
}
