import 'package:automoment/app/constants/app_color.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:vibration/vibration.dart';

import '../../../constants/app_text_styles.dart';
import '../../shared/support_button.dart';
import '../controllers/check_in_controller.dart';

class CheckInView extends StatelessWidget {
  CheckInView({super.key});

  final CheckInController controller = Get.put(CheckInController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Check In', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Scaffold(
        body: Stack(
          children: [
            MobileScanner(
              controller: controller.scanController,
              onDetect: (capture) {
                if (!controller.qrcodeDetected.value) {
                  final List<Barcode> barcodes = capture.barcodes;
                  if (barcodes.isNotEmpty) {
                    controller.qrcodeDetected.value = true;

                    final String code = barcodes.first.rawValue ?? '';
                    debugPrint('Barcode found! $code');
                    Vibration.vibrate();

                    var expectedString =
                        'automoment://event/${controller.event.value.id}';

                    if (code == expectedString) {
                      showFormDecalAndTransponder(context);
                    } else {
                      showScanInvalidMsg(context);
                      controller.scanController.start();
                    }
                  }
                }
              },
            ),
            Positioned.fill(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                  Container(
                    decoration: const BoxDecoration(color: Colors.white),
                    padding: const EdgeInsets.all(40),
                    width: Get.width,
                    child: Text(
                      "Align QR code to fill inside the frame",
                      style: AppTextStyles.normalText
                          .copyWith(color: Colors.black54, fontSize: 18),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const Expanded(
                    child: Center(
                      child: Image(
                        image: AssetImage('assets/images/scan_overlay.png'),
                        width: 300,
                        height: 300,
                      ),
                    ),
                  )
                ])),
          ],
        ),
      ),
    );
  }

  void showFormDecalAndTransponder(BuildContext context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        //enableDrag: true,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return PopScope(
            canPop: true,
            child: Material(
              color: Colors.transparent,
              child: SafeArea(
                top: false,
                child: Obx(() {
                  return SingleChildScrollView(
                    physics: const ScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics()),
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    // reverse: false,
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20)),
                        boxShadow: [
                          BoxShadow(
                              offset: Offset(0, -5),
                              blurRadius: 15,
                              color: Colors.black12),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 50),
                        child: (!controller.isCheckInSuccess.value)
                            ? Form(
                                key: controller.formKey,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const SizedBox(height: 20),
                                    Text(
                                        "Please enter your decal number and transponder number",
                                        style: AppTextStyles.titleText
                                            .copyWith(fontSize: 16)),
                                    const SizedBox(height: 10),
                                    TextFormField(
                                      controller: controller.decalController,
                                      keyboardType: TextInputType.text,
                                      autocorrect: false,
                                      decoration: const InputDecoration(
                                        labelText: "Decal Number",
                                        hintText: "",
                                      ),
                                      validator: (value) {
                                        if (value!.isEmpty) {
                                          return 'Required';
                                        }
                                        return null;
                                      },
                                    ),
                                    const SizedBox(height: 10),
                                    TextFormField(
                                      controller:
                                          controller.transponderController,
                                      keyboardType: TextInputType.text,
                                      autocorrect: false,
                                      decoration: const InputDecoration(
                                        labelText: "Transponder Number",
                                        hintText: "",
                                      ),
                                      validator: (value) {
                                        if (value!.isEmpty) {
                                          return 'Required';
                                        }
                                        return null;
                                      },
                                    ),
                                    const SizedBox(height: 20),

                                    // button
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 40),
                                      child: ElevatedButton(
                                          style: ButtonStyle(
                                              foregroundColor:
                                                  WidgetStateProperty.all<Color>(
                                                      Colors.white),
                                              backgroundColor: WidgetStateProperty.all<Color>(
                                                  AppColor.primaryButtonColor),
                                              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                                  RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              18.0),
                                                      side: const BorderSide(
                                                          color: AppColor
                                                              .primaryButtonColor)))),
                                          onPressed: () async {
                                            if (controller.formKey.currentState!
                                                .validate()) {
                                              // perform check in here

                                              if (await controller.checkIn()) {
                                                controller.isCheckInSuccess
                                                    .value = true;
                                              } else {
                                                controller.isCheckInSuccess
                                                    .value = false;
                                                //showCheckInFailedMsg(context, controller.checkInMsg);
                                              }
                                            }
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.only(
                                                left: 20, right: 20),
                                            height: 50,
                                            child: const Center(
                                              child: Text("Check In",
                                                  style:
                                                      TextStyle(fontSize: 16)),
                                            ),
                                          )),
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                ),
                              )
                            : Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const SizedBox(height: 20),
                                  Image.asset(
                                    'assets/images/check_in_ok.png',
                                    height: 60,
                                  ),
                                  const SizedBox(height: 20),
                                  Text(
                                    controller.checkInMsg,
                                    style: AppTextStyles.normalText
                                        .copyWith(fontSize: 16),
                                  ),
                                  const SizedBox(height: 20),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 40),
                                    child: Text(
                                      controller.event.value.title!,
                                      style: AppTextStyles.titleText
                                          .copyWith(fontSize: 16, height: 1.6),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  const SizedBox(height: 20),
                                  // button
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 40),
                                    child: ElevatedButton(
                                        style: ButtonStyle(
                                            foregroundColor: WidgetStateProperty
                                                .all<Color>(Colors.white),
                                            backgroundColor: WidgetStateProperty.all<Color>(
                                                AppColor.primaryButtonColor),
                                            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                                RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            18.0),
                                                    side: const BorderSide(
                                                        color: AppColor
                                                            .primaryButtonColor)))),
                                        onPressed: () {
                                          controller.qrcodeDetected.value =
                                              false;
                                          controller.updateBookingStatus();
                                          Navigator.of(context).pop();
                                          Navigator.of(context).pop();
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.only(
                                              left: 20, right: 20),
                                          height: 50,
                                          child: const Center(
                                            child: Text("Ok",
                                                style: TextStyle(fontSize: 16)),
                                          ),
                                        )),
                                  ),
                                  const SizedBox(height: 20),
                                ],
                              ),
                      ),
                    ),
                  );
                }),
              ),
            ),
          );
        }).then((value) {
      controller.qrcodeDetected.value = false;
      controller.scanController.start();
    });
  }

  void showScanInvalidMsg(BuildContext context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        enableDrag: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            child: Material(
              color: Colors.transparent,
              child: SafeArea(
                top: false,
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  reverse: false,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                            offset: Offset(0, -5),
                            blurRadius: 15,
                            color: Colors.black12),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 20),
                        Image.asset(
                          'assets/images/minus.png',
                          height: 60,
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Text(
                              'You scan invalid QR code for this event. Please try the correct one.',
                              style: AppTextStyles.normalText.copyWith(
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Text(
                            controller.event.value.title!,
                            style: AppTextStyles.titleText
                                .copyWith(fontSize: 16, height: 1.6),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(height: 20),
                        // button
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: ElevatedButton(
                              style: ButtonStyle(
                                  foregroundColor:
                                      WidgetStateProperty.all<Color>(
                                          Colors.white),
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColor.primaryButtonColor),
                                  shape: WidgetStateProperty.all<
                                          RoundedRectangleBorder>(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(18.0),
                                          side: const BorderSide(
                                              color: AppColor
                                                  .primaryButtonColor)))),
                              onPressed: () {
                                controller.qrcodeDetected.value = false;
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                height: 50,
                                child: const Center(
                                  child: Text("Try again",
                                      style: TextStyle(fontSize: 16)),
                                ),
                              )),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  void showScanFailedMsg(BuildContext context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        enableDrag: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            child: Material(
              color: Colors.transparent,
              child: SafeArea(
                top: false,
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  reverse: false,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                            offset: Offset(0, -5),
                            blurRadius: 15,
                            color: Colors.black12),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 20),
                        Image.asset(
                          'assets/images/minus.png',
                          height: 60,
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child:
                              Text('Failed to scan QR code. Please try again.',
                                  style: AppTextStyles.normalText.copyWith(
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Text(
                            controller.event.value.title!,
                            style: AppTextStyles.titleText
                                .copyWith(fontSize: 16, height: 1.6),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(height: 20),
                        // button
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: ElevatedButton(
                              style: ButtonStyle(
                                  foregroundColor:
                                      WidgetStateProperty.all<Color>(
                                          Colors.white),
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColor.primaryButtonColor),
                                  shape: WidgetStateProperty.all<
                                          RoundedRectangleBorder>(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(18.0),
                                          side: const BorderSide(
                                              color: AppColor
                                                  .primaryButtonColor)))),
                              onPressed: () {
                                controller.qrcodeDetected.value = false;
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                height: 50,
                                child: const Center(
                                  child: Text("Try again",
                                      style: TextStyle(fontSize: 16)),
                                ),
                              )),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  void showCheckInFailedMsg(BuildContext context, String msg) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        enableDrag: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            child: Material(
              color: Colors.transparent,
              child: SafeArea(
                top: false,
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  reverse: false,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                            offset: Offset(0, -5),
                            blurRadius: 15,
                            color: Colors.black12),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 20),
                        Image.asset(
                          'assets/images/minus.png',
                          height: 60,
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Text(msg,
                              style: AppTextStyles.normalText.copyWith(
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Text(
                            controller.event.value.title!,
                            style: AppTextStyles.titleText
                                .copyWith(fontSize: 16, height: 1.6),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(height: 20),
                        // button
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: ElevatedButton(
                              style: ButtonStyle(
                                  foregroundColor:
                                      WidgetStateProperty.all<Color>(
                                          Colors.white),
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColor.primaryButtonColor),
                                  shape: WidgetStateProperty.all<
                                          RoundedRectangleBorder>(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(18.0),
                                          side: const BorderSide(
                                              color: AppColor
                                                  .primaryButtonColor)))),
                              onPressed: () {
                                controller.qrcodeDetected.value = false;
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                height: 50,
                                child: const Center(
                                  child: Text("Try again",
                                      style: TextStyle(fontSize: 16)),
                                ),
                              )),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }
}
