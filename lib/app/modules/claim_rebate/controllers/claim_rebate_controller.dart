import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/models/event_modal.dart';
import 'package:automoment/app/modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class ClaimRebateController extends BaseScreenController {
  @override
  String get screenName => 'Claim Reward';

  final linkController = TextEditingController();
  final userController = Get.find<UserController>();

  var event = Event().obs;
  var image = "".obs;
  var activity = "".obs;
  var isSubmitted = false.obs;
  final submissionStatus = ''.obs;

  @override
  Future<void> onInit() async {
    event.value = Get.arguments;
    await fetchRebate();
    super.onInit();
  }

  @override
  void onClose() {
    linkController.dispose();
    super.onClose();
  }

  Future<void> submitClaim() async {
    await claimRebate();
  }

  Future<void> fetchRebate() async {
    try {
      EasyLoading.show(status: 'loading...');
      var response =
          await ApiClient.getRebate(userController.getToken(), event.value.id!);
      var jsonResponse = jsonDecode(response);
      image.value = jsonResponse['rebate']['image'] ?? '';
      activity.value = jsonResponse['rebate']['activity'] ?? '';
      isSubmitted.value = jsonResponse['is_submitted'] ?? false;
      submissionStatus.value = jsonResponse['submission_status'] ?? '';

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e as String?);
    }
  }

  Future<void> claimRebate() async {
    try {
      EasyLoading.show(status: 'loading...');
      var response = await ApiClient.claimRebate(
          userController.getToken(), event.value.id!, linkController.text);

      bool success = jsonDecode(response)['success'] ?? false;
      String msg = jsonDecode(response)['message'] ?? "";

      if (success) {
        showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return KMessageDialogView(
              content: msg,
              callback: () async {
                await fetchRebate();
                Navigator.of(context).pop();
              },
            );
          },
        );
      } else {
        showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return KMessageDialogView(
              content: msg,
              callback: () {
                Navigator.of(context).pop();
              },
            );
          },
        );
      }

      debugPrint("image ${image.value}");
      debugPrint("activity ${activity.value}");

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint(e as String?);
    }
  }
}
