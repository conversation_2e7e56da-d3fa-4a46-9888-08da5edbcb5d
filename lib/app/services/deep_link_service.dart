import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
// import 'package:flutter/widgets.dart'; // Not directly used, can be removed if not needed elsewhere
import 'package:get/get.dart'; // Keep for GetxService, Get.find, Get.put if still used
import 'package:get_storage/get_storage.dart';

import '../modules/bottombar/controllers/bottombar_controller.dart';
import '../routes/app_router.dart'; // Corrected import

/// Enum representing the different types of deep link destinations
enum DeepLinkType {
  event,
  news,
  lifestyle,
  prodDetail,
  car,
  leaderboardDetail,
  leaderboardAllTime,
  leaderboardThisYear,
  upcomingEvents,
  latestNews,
  account,
  reward,
  store,
  unknown
}

/// Model class for deep link data
class DeepLinkData {
  final DeepLinkType type;
  final int? id;
  final String? viewType;

  const DeepLinkData({
    required this.type,
    this.id,
    this.viewType,
  });

  @override
  String toString() =>
      'DeepLinkData(type: $type, id: $id, viewType: $viewType)';
}

class DeepLinkService extends GetxService {
  final AppLinks _appLinks = AppLinks();
  final StreamController<String> _deepLinkStreamController =
      StreamController<String>.broadcast();
  final storage = GetStorage();
  static const String deepLinkKey = 'deep_link_key';

  static const Map<DeepLinkType, String> _pendingIdKeys = {
    DeepLinkType.event: 'pending_event_id',
    DeepLinkType.news: 'pending_news_id',
    DeepLinkType.lifestyle: 'pending_lifestyle_id',
    DeepLinkType.prodDetail: 'pending_prod_detail_id',
    DeepLinkType.car: 'pending_car_id',
    DeepLinkType.leaderboardDetail: 'pending_leaderboard_detail_id',
  };

  static final Map<DeepLinkType, String> _routeMap = {
    DeepLinkType.event: AppRoutePaths.eventsDetail,
    DeepLinkType.news: AppRoutePaths.newsDetail,
    DeepLinkType.lifestyle: AppRoutePaths.rewardLifestyleDetail,
    DeepLinkType.prodDetail: AppRoutePaths.productDetail,
    DeepLinkType.car: AppRoutePaths.carListingDetail,
    DeepLinkType.leaderboardDetail: AppRoutePaths.leaderboardDetail,
  };

  static const Map<DeepLinkType, int> _tabIndices = {
    DeepLinkType.upcomingEvents: 2,
    DeepLinkType.latestNews: 1,
    DeepLinkType.account:
        6, // Assuming tab index 6 for account based on original main.dart
    DeepLinkType.reward: 4,
    DeepLinkType.leaderboardAllTime: 3,
    DeepLinkType.leaderboardThisYear: 3,
    DeepLinkType.store: 5
  };

  static const Map<DeepLinkType, String> _tabNavigationKeys = {
    DeepLinkType.upcomingEvents: 'navigate_to_upcoming_events_tab',
    DeepLinkType.latestNews: 'navigate_to_latest_news_tab',
    DeepLinkType.account: 'navigate_to_account_tab',
    DeepLinkType.reward: 'navigate_to_reward_tab',
    DeepLinkType.leaderboardAllTime: 'navigate_to_leaderboard_all_time_tab',
    DeepLinkType.leaderboardThisYear: 'navigate_to_leaderboard_this_year_tab',
    DeepLinkType.store: 'navigate_to_store_tab'
  };

  Stream<String> get deepLinkStream => _deepLinkStreamController.stream;

  String? _latestLink;
  String? get latestLink => _latestLink;

  bool _hasPendingDeepLink = false;
  bool get hasPendingDeepLink => _hasPendingDeepLink;

  int? getPendingEventId() {
    return storage.read<int>(_pendingIdKeys[DeepLinkType.event]!);
  }

  Future<DeepLinkService> init() async {
    debugPrint('DeepLinkService: Initializing');

    _appLinks.uriLinkStream.listen((uri) {
      debugPrint(
          'DeepLinkService: Received link from stream: ${uri.toString()}');
      _handleDeepLink(uri.toString(), false);
    });

    try {
      debugPrint('DeepLinkService: Checking for initial link (cold start)');
      final appLink = await _appLinks.getInitialLink();
      debugPrint('DeepLinkService: Initial link result: $appLink');

      if (appLink != null) {
        _latestLink = appLink.toString();
        _hasPendingDeepLink = true;
        debugPrint('DeepLinkService: Got initial link: $_latestLink');
        storage.write(deepLinkKey, _latestLink);
        debugPrint('DeepLinkService: Stored deep link for cold start handling');
      } else {
        final storedLink = storage.read<String>(deepLinkKey);
        if (storedLink != null) {
          debugPrint('DeepLinkService: Found stored deep link: $storedLink');
          _latestLink = storedLink;
          _hasPendingDeepLink = true;
        } else {
          debugPrint('DeepLinkService: No initial link found');
        }
      }
    } catch (e) {
      debugPrint('DeepLinkService: Error getting initial app link: $e');
    }
    return this;
  }

  void _handleDeepLink(String link, bool isColdStart) {
    debugPrint('Deep link received: $link, isColdStart: $isColdStart');
    _latestLink = link;
    _deepLinkStreamController.add(link);

    if (isColdStart) {
      storage.write(deepLinkKey, link);
      _hasPendingDeepLink = true;
      debugPrint('Stored deep link for processing after app initialization');
    } else {
      final deepLinkData = _parseDeepLinkData(link);
      _navigateBasedOnDeepLinkData(deepLinkData, false);
    }
  }

  DeepLinkData _parseDeepLinkData(String link) {
    final uri = Uri.parse(link);
    final pathSegments = uri.pathSegments;

    if (pathSegments.isEmpty) {
      debugPrint('No path segments found in link: $link');
      return const DeepLinkData(type: DeepLinkType.unknown);
    }
    debugPrint('Path segments: $pathSegments');

    if (pathSegments.length >= 2) {
      final type = pathSegments[0].trim();
      final idStr = pathSegments[1].trim();
      if (idStr.isNotEmpty) {
        try {
          final id = int.parse(idStr);
          switch (type) {
            case 'event':
              return DeepLinkData(type: DeepLinkType.event, id: id);
            case 'news':
              return DeepLinkData(type: DeepLinkType.news, id: id);
            case 'lifestyle':
              return DeepLinkData(type: DeepLinkType.lifestyle, id: id);
            case 'prod-detail':
              return DeepLinkData(type: DeepLinkType.prodDetail, id: id);
            case 'car':
              return DeepLinkData(type: DeepLinkType.car, id: id);
            case 'lb-detail':
              return DeepLinkData(type: DeepLinkType.leaderboardDetail, id: id);
          }
        } catch (e) {
          debugPrint('Error parsing ID: $e');
        }
      }
    }

    final firstSegment = pathSegments[0];
    switch (firstSegment) {
      case 'leaderboard-all-time':
        return const DeepLinkData(
            type: DeepLinkType.leaderboardAllTime, viewType: 'all_time');
      case 'leaderboard-this-year':
        return const DeepLinkData(
            type: DeepLinkType.leaderboardThisYear, viewType: 'this_year');
      case 'upcoming-events':
        return const DeepLinkData(type: DeepLinkType.upcomingEvents);
      case 'latest-news':
        return const DeepLinkData(type: DeepLinkType.latestNews);
      case 'account':
        return const DeepLinkData(type: DeepLinkType.account);
      case 'reward':
        return const DeepLinkData(type: DeepLinkType.reward);
      case 'storehome':
        return const DeepLinkData(type: DeepLinkType.store);
      default:
        return const DeepLinkData(type: DeepLinkType.latestNews); // Or unknown
    }
  }

  void _navigateBasedOnDeepLinkData(DeepLinkData data, bool isColdStart) {
    debugPrint(
        'Navigating based on deep link data: $data, isColdStart: $isColdStart');
    if (data.id != null && _routeMap.containsKey(data.type)) {
      if (isColdStart) {
        final key = _pendingIdKeys[data.type]!;
        storage.write(key, data.id);
        debugPrint(
            'Stored ${data.type} ID ${data.id} for processing after app initialization');
      } else {
        _navigateToDetailPage(data.type, data.id!);
      }
      return;
    }
    if (_tabIndices.containsKey(data.type)) {
      _navigateToTab(data.type, data.viewType, isColdStart);
    }
  }

  void _navigateToTab(DeepLinkType type, String? viewType, bool isColdStart) {
    final storageKey = 'navigate_to_${type.toString().split('.').last}_tab';
    debugPrint(
        'Handling navigation to ${type.toString().split('.').last} tab, isColdStart: $isColdStart');

    if (isColdStart) {
      storage.write(storageKey, true);
      if (viewType != null) {
        storage.write('${type.toString().split('.').last}_view_type', viewType);
      }
      debugPrint(
          'Stored flag to navigate to ${type.toString().split('.').last} tab after app initialization');
    } else {
      _directNavigateToTab(type, viewType);
    }
  }

  void _setViewTypePreference(DeepLinkType type, String? viewType) {
    if (viewType == null) return;
    final typeStr = type.toString().split('.').last;
    try {
      debugPrint('Setting $typeStr view type to: $viewType');
      storage.write('preferred_${typeStr}_view', viewType);
    } catch (e) {
      debugPrint('Error setting $typeStr view type: $e');
    }
  }

  Future<void> _directNavigateToTab(DeepLinkType type, String? viewType) async {
    final tabIndex = _tabIndices[type]!;
    const String targetRoute = AppRoutePaths.bottomBar;
    debugPrint('Direct navigation to $targetRoute with tabIndex: $tabIndex');

    await Future.delayed(const Duration(milliseconds: 500)); // Shorter delay

    try {
      if (AppRouter.router.routerDelegate.currentConfiguration.uri.toString() !=
          AppRoutePaths.bottomBar) {
        debugPrint(
            'Current route is not BOTTOMBAR, navigating there first using GoRouter');
        AppRouter.router.go(AppRoutePaths.bottomBar);
        await Future.delayed(
            const Duration(milliseconds: 500)); // Wait for navigation
      }
      _navigateToTabAfterBottomBar(type, tabIndex, viewType);
    } catch (e) {
      debugPrint('Error during direct navigation to tab: $e');
      storage.write('initial_tab_index', tabIndex);
    }
  }

  void _navigateToTabAfterBottomBar(
      DeepLinkType type, int tabIndex, String? viewType) {
    try {
      final bottombarController = Get.find<BottombarController>();
      bottombarController.changePage(tabIndex);
      debugPrint(
          'Changed to ${type.toString().split('.').last} tab index $tabIndex');

      if (viewType != null) {
        _setViewTypePreference(type, viewType);
      }

      final storageKey = 'navigate_to_${type.toString().split('.').last}_tab';
      storage.remove(storageKey);
      if (viewType != null) {
        storage.remove('${type.toString().split('.').last}_view_type');
      }
    } catch (e) {
      debugPrint(
          'Error during navigation to ${type.toString().split('.').last} tab: $e');
    }
  }

  void _navigateToDetailPage(DeepLinkType type, int id) {
    final route = _routeMap[type]!;
    final key = _pendingIdKeys[type]!;
    debugPrint('Attempting to navigate to $route with ID: $id');

    Future.delayed(const Duration(milliseconds: 100), () {
      // Shorter delay
      try {
        dynamic arguments;
        switch (type) {
          case DeepLinkType.event:
          case DeepLinkType.news:
          case DeepLinkType.lifestyle:
          case DeepLinkType.leaderboardDetail:
          case DeepLinkType.prodDetail:
            arguments =
                '{"id": $id}'; // Consider if GoRouter needs this as a map or query param
            break;
          default:
            arguments = id;
            break;
        }
        debugPrint(
            'Navigating to $route with arguments: $arguments using GoRouter');
        AppRouter.router.push(route,
            extra: arguments); // Use 'extra' for complex objects or maps

        storage.remove(deepLinkKey);
        storage.remove(key);
        _hasPendingDeepLink = false;
        debugPrint(
            'Navigation completed successfully and cleared stored deep link');
      } catch (e) {
        debugPrint('Error during navigation: $e');
        storage.write('initial_${type.toString().split('.').last}_id', id);
      }
    });
  }

  Future<void> _directNavigateToDetailPage(DeepLinkType type, int id) async {
    final route = _routeMap[type]!;
    debugPrint('Direct navigation to $route with ID: $id');
    await Future.delayed(const Duration(milliseconds: 500));

    try {
      if (AppRouter.router.routerDelegate.currentConfiguration.uri.toString() !=
          AppRoutePaths.bottomBar) {
        debugPrint(
            'Current route is not BOTTOMBAR, navigating there first using GoRouter');
        AppRouter.router.go(AppRoutePaths.bottomBar);
        await Future.delayed(const Duration(milliseconds: 500));
      }
      _navigateToDetailPageAfterBottomBar(type, id);
    } catch (e) {
      debugPrint('Error during direct navigation: $e');
      storage.write('initial_${type.toString().split('.').last}_id', id);
    }
  }

  void _navigateToDetailPageAfterBottomBar(DeepLinkType type, int id) {
    final route = _routeMap[type]!;
    final key = _pendingIdKeys[type]!;
    try {
      dynamic arguments;
      switch (type) {
        case DeepLinkType.event:
        case DeepLinkType.news:
        case DeepLinkType.lifestyle:
        case DeepLinkType.leaderboardDetail:
        case DeepLinkType.prodDetail:
          arguments = {
            'id': id.toString()
          }; // Pass as map for GoRouter path parameters or extra
          // If using path parameters like /event/:id, then it should be AppRouter.router.push('/event/$id');
          // If using extra: AppRouter.router.push(AppRoutePaths.eventsDetail, extra: {'id': id});
          // For now, assuming 'extra' is the way to go with current AppRoutePaths setup.
          break;
        default:
          arguments = {'id': id.toString()}; // Default to map as well
          break;
      }
      debugPrint(
          'Navigating to $route with arguments: $arguments using GoRouter');
      AppRouter.router.push(route, extra: arguments);

      storage.remove(deepLinkKey);
      storage.remove(key);
      _hasPendingDeepLink = false;
      debugPrint('Direct navigation completed successfully');
    } catch (e) {
      debugPrint('Error during navigation after bottom bar: $e');
      storage.write('initial_${type.toString().split('.').last}_id', id);
    }
  }

  void processPendingDeepLinks() {
    debugPrint(
        'Processing pending deep links, hasPendingDeepLink: $_hasPendingDeepLink');
    if (!_hasPendingDeepLink && _latestLink == null) {
      // Also check _latestLink
      debugPrint('No pending deep link or latest link to process.');
      return;
    }

    // Prioritize processing stored IDs first
    for (final entry in _pendingIdKeys.entries) {
      final type = entry.key;
      final key = entry.value;
      final pendingId = storage.read<int>(key);
      if (pendingId != null) {
        debugPrint(
            'Found pending ${type.toString().split('.').last} ID: $pendingId');
        _directNavigateToDetailPage(type, pendingId);
        storage.remove(key); // Clear after processing
        // Correctly re-evaluate _hasPendingDeepLink
        _hasPendingDeepLink =
            _pendingIdKeys.values.any((k) => storage.hasData(k)) ||
                _tabNavigationKeys.values.any((k) => storage.hasData(k));
        if (!_hasPendingDeepLink) {
          storage
              .remove(deepLinkKey); // Clear main key if no more pending items
        }
        return;
      }
    }

    for (final entry in _tabNavigationKeys.entries) {
      final type = entry.key;
      final key = entry.value;
      final navigateToTab = storage.read<bool>(key);
      if (navigateToTab == true) {
        debugPrint(
            'Found flag to navigate to ${type.toString().split('.').last} tab');
        String? viewType;
        if (type == DeepLinkType.leaderboardAllTime ||
            type == DeepLinkType.leaderboardThisYear) {
          viewType = storage
              .read<String>('${type.toString().split('.').last}_view_type');
        }
        _directNavigateToTab(type, viewType);
        storage.remove(key); // Clear after processing
        if (viewType != null) {
          storage.remove('${type.toString().split('.').last}_view_type');
        }
        // Correctly re-evaluate _hasPendingDeepLink
        _hasPendingDeepLink =
            _pendingIdKeys.values.any((k) => storage.hasData(k)) ||
                _tabNavigationKeys.values.any((k) => storage.hasData(k));
        if (!_hasPendingDeepLink) {
          storage
              .remove(deepLinkKey); // Clear main key if no more pending items
        }
        return;
      }
    }

    // If no specific pending IDs or tab flags, but _latestLink exists (e.g. from cold start not yet parsed into specific actions)
    if (_latestLink != null) {
      debugPrint('Processing stored _latestLink: $_latestLink');
      final deepLinkData = _parseDeepLinkData(_latestLink!);
      // Check if this link results in an action that would have stored a pending ID/flag
      // If not, or if it's a general link, navigate based on it.
      // This prevents re-processing if it already led to a pendingId/flag.
      bool wasHandledByPending = (_pendingIdKeys
                  .containsKey(deepLinkData.type) &&
              storage.read<int>(_pendingIdKeys[deepLinkData.type]!) != null) ||
          (_tabNavigationKeys.containsKey(deepLinkData.type) &&
              storage.read<bool>(_tabNavigationKeys[deepLinkData.type]!) ==
                  true);

      if (!wasHandledByPending) {
        _navigateBasedOnDeepLinkData(
            deepLinkData, false); // Treat as a warm start navigation now
      }
      storage.remove(
          deepLinkKey); // Clear the generic link after attempting to process
      _latestLink = null; // Clear after processing
    }
    _hasPendingDeepLink = false; // Reset after attempting all processing
  }

  @override
  void onClose() {
    _deepLinkStreamController.close();
    super.onClose();
  }
}
